import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

/**
 * 全局日期处理工具函数
 * 解决 Ant Design Vue 日期选择器与 dayjs 兼容性问题
 *
 * 核心原则：
 * 1. 统一使用字符串格式进行数据绑定，避免 locale 问题
 * 2. 参考事件上报模块的成功实践
 * 3. 确保与现有模块的一致性
 */

/**
 * 格式化时间为标准字符串格式（参考事件上报模块的成功实践）
 * @param timeValue 任意格式的时间值
 * @returns 标准格式的时间字符串或null
 */
export const formatTimeToString = (timeValue: any): string | null => {
  if (!timeValue) return null

  try {
    // 处理dayjs对象
    if (timeValue && typeof timeValue === 'object' && (timeValue.$isDayjsObject || typeof timeValue.format === 'function')) {
      return timeValue.format('YYYY-MM-DD HH:mm:ss')
    }

    // 处理字符串格式
    if (typeof timeValue === 'string') {
      // 如果已经是标准格式，直接返回
      if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeValue)) {
        return timeValue
      }

      // 尝试解析其他格式
      const dayjsObj = dayjs(timeValue)
      if (dayjsObj.isValid()) {
        return dayjsObj.format('YYYY-MM-DD HH:mm:ss')
      }
    }

    // 处理Date对象
    if (timeValue instanceof Date) {
      return dayjs(timeValue).format('YYYY-MM-DD HH:mm:ss')
    }

    // 处理时间戳
    if (typeof timeValue === 'number') {
      return dayjs(timeValue).format('YYYY-MM-DD HH:mm:ss')
    }

    console.warn('无法格式化的时间值:', timeValue, typeof timeValue)
    return null

  } catch (error) {
    console.error('时间格式化失败:', timeValue, error)
    return null
  }
}

/**
 * 解析日期字符串为dayjs对象（用于显示）
 * @param dateStr 日期字符串或dayjs对象
 * @returns dayjs对象或null
 */
export const parseDateString = (dateStr: string | Dayjs | null | undefined): Dayjs | null => {
  if (!dateStr) return null

  // 如果已经是dayjs对象，直接返回
  if (dayjs.isDayjs(dateStr)) {
    return dateStr
  }

  // 处理字符串格式
  if (typeof dateStr === 'string') {
    try {
      const trimmedStr = dateStr.trim()
      const dayjsObj = dayjs(trimmedStr)

      // 验证日期有效性
      if (!dayjsObj.isValid()) {
        console.warn('无效的日期字符串:', trimmedStr)
        return null
      }

      return dayjsObj
    } catch (error) {
      console.error('日期解析失败:', dateStr, error)
      return null
    }
  }

  console.warn('不支持的日期类型:', typeof dateStr, dateStr)
  return null
}

/**
 * 确保日期对象是 dayjs 对象（兼容旧版本）
 * @param date 任意格式的日期
 * @returns dayjs 对象或 null
 */
export const ensureDayjsObject = (date: any): Dayjs | null => {
  return parseDateString(date)
}

/**
 * 格式化日期为字符串
 * @param date 日期对象
 * @param format 格式字符串，默认 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: any, format: string = 'YYYY-MM-DD'): string => {
  const dayjsDate = ensureDayjsObject(date)
  return dayjsDate ? dayjsDate.format(format) : ''
}

/**
 * 格式化日期时间为字符串
 * @param date 日期对象
 * @param format 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: any, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  const dayjsDate = ensureDayjsObject(date)
  return dayjsDate ? dayjsDate.format(format) : ''
}

/**
 * 创建安全的日期选择器属性
 * @param value 当前值
 * @param onChange 变更回调
 * @returns 日期选择器属性对象
 */
export const createDatePickerProps = (value: any, onChange: (date: Dayjs | null) => void) => {
  return {
    value: ensureDayjsObject(value),
    onChange: (date: any) => onChange(ensureDayjsObject(date))
  }
}

/**
 * 批量处理日期字段
 * @param obj 包含日期字段的对象
 * @param dateFields 日期字段名数组
 * @returns 处理后的对象
 */
export const processDatesInObject = (obj: any, dateFields: string[]) => {
  if (!obj) return obj
  
  const result = { ...obj }
  dateFields.forEach(field => {
    if (result[field]) {
      result[field] = ensureDayjsObject(result[field])
    }
  })
  
  return result
}

/**
 * 获取当前时间的 dayjs 对象
 * @returns 当前时间的 dayjs 对象
 */
export const now = (): Dayjs => dayjs()

/**
 * 检查日期是否有效
 * @param date 日期对象
 * @returns 是否有效
 */
export const isValidDate = (date: any): boolean => {
  const dayjsDate = ensureDayjsObject(date)
  return dayjsDate ? dayjsDate.isValid() : false
}
