<!--
  参会信息表单组件
  
  基于EventReportPage.vue的组件设计模式，提供分析会议信息的表单录入功能
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <div class="meeting-info-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      :disabled="readonly"
      @finish="handleSubmit"
      @valuesChange="handleValuesChange"
    >
      <!-- 会议基本信息 -->
      <div class="form-section">
        <h4 class="section-title">会议基本信息</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="会议时间"
              name="meetingTime"
              :rules="[{ required: true, message: '请选择会议时间' }]"
            >
              <a-date-picker
                :value="ensureDayjsObject(formData.meetingTime)"
                @change="(date) => formData.meetingTime = ensureDayjsObject(date)"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="请选择会议时间"
                style="width: 100%"
                :disabled="readonly"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              label="会议地点"
              name="meetingLocation"
              :rules="[{ required: true, message: '请输入会议地点' }]"
            >
              <a-input
                v-model:value="formData.meetingLocation"
                placeholder="请输入会议地点"
                :maxlength="100"
                show-count
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="会议主持人"
              name="chairperson"
              :rules="[{ required: true, message: '请选择会议主持人' }]"
            >
              <a-select
                v-model:value="formData.chairperson"
                placeholder="请选择会议主持人"
                show-search
                :filter-option="filterOption"
                :loading="loadingUsers"
                @dropdown-visible-change="handleUserDropdown"
              >
                <a-select-option
                  v-for="user in userList"
                  :key="user.id"
                  :value="user.id"
                >
                  {{ user.name }} ({{ user.department }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              label="会议类型"
              name="meetingType"
            >
              <a-select
                v-model:value="formData.meetingType"
                placeholder="请选择会议类型"
              >
                <a-select-option value="regular">常规分析会</a-select-option>
                <a-select-option value="emergency">紧急分析会</a-select-option>
                <a-select-option value="expert">专家会诊</a-select-option>
                <a-select-option value="multidisciplinary">多学科讨论</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 参会人员 -->
      <div class="form-section">
        <div class="section-header">
          <h4 class="section-title">参会人员</h4>
          <a-button 
            type="dashed" 
            size="small" 
            @click="handleAddParticipant"
            :disabled="readonly"
          >
            <template #icon><PlusOutlined /></template>
            添加参会人员
          </a-button>
        </div>
        
        <div v-if="!formData.participants.length" class="empty-participants">
          <a-empty description="暂无参会人员" size="small">
            <a-button type="primary" size="small" @click="handleAddParticipant" :disabled="readonly">
              添加第一个参会人员
            </a-button>
          </a-empty>
        </div>
        
        <div v-else class="participants-list">
          <div 
            v-for="(participant, index) in formData.participants" 
            :key="participant.id || index"
            class="participant-item"
          >
            <a-row :gutter="12" align="middle">
              <a-col :span="6">
                <a-form-item
                  :name="['participants', index, 'userId']"
                  :rules="[{ required: true, message: '请选择参会人员' }]"
                >
                  <a-select
                    v-model:value="participant.userId"
                    placeholder="选择人员"
                    show-search
                    :filter-option="filterOption"
                    :loading="loadingUsers"
                    @change="(value) => handleParticipantChange(index, 'userId', value)"
                  >
                    <a-select-option
                      v-for="user in userList"
                      :key="user.id"
                      :value="user.id"
                    >
                      {{ user.name }} ({{ user.department }})
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              
              <a-col :span="5">
                <a-form-item
                  :name="['participants', index, 'role']"
                  :rules="[{ required: true, message: '请输入角色' }]"
                >
                  <a-input
                    v-model:value="participant.role"
                    placeholder="角色"
                    @change="(e) => handleParticipantChange(index, 'role', e.target.value)"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="5">
                <a-form-item :name="['participants', index, 'expertise']">
                  <a-input
                    v-model:value="participant.expertise"
                    placeholder="专业领域"
                    @change="(e) => handleParticipantChange(index, 'expertise', e.target.value)"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="6">
                <a-form-item :name="['participants', index, 'contribution']">
                  <a-input
                    v-model:value="participant.contribution"
                    placeholder="主要贡献"
                    @change="(e) => handleParticipantChange(index, 'contribution', e.target.value)"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="2">
                <a-button 
                  type="text" 
                  danger 
                  size="small"
                  @click="handleRemoveParticipant(index)"
                  :disabled="readonly"
                >
                  <template #icon><DeleteOutlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <!-- 会议记录 -->
      <div class="form-section">
        <h4 class="section-title">会议记录</h4>
        
        <a-form-item
          label="会议议程"
          name="agenda"
        >
          <a-textarea
            v-model:value="formData.agenda"
            placeholder="请输入会议议程"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item
          label="会议纪要"
          name="minutes"
        >
          <a-textarea
            v-model:value="formData.minutes"
            placeholder="请输入会议纪要"
            :rows="4"
            :maxlength="1000"
            show-count
          />
        </a-form-item>
      </div>

      <!-- 表单操作 -->
      <div v-if="!readonly" class="form-actions">
        <a-space>
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" html-type="submit" :loading="saving">
            保存会议信息
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import type { Participant } from '@/types/moduleCommunication'
import dayjs from 'dayjs'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

// ============================ Props定义 ============================

export interface MeetingInfo {
  meetingTime?: any // dayjs对象或null
  meetingLocation?: string
  chairperson?: string
  meetingType?: string
  participants: Participant[]
  agenda?: string
  minutes?: string
}

export interface Props {
  value?: MeetingInfo
  readonly?: boolean
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: () => ({
    participants: []
  }),
  readonly: false,
  saving: false
})

// ============================ Emits定义 ============================

const emit = defineEmits<{
  'update:value': [value: MeetingInfo]
  change: [value: MeetingInfo]
  submit: [value: MeetingInfo]
}>()

// ============================ 响应式数据 ============================

const formRef = ref<FormInstance>()
const loadingUsers = ref(false)
const userList = ref<Array<{ id: string; name: string; department: string }>>([])

// 表单数据
const formData = reactive<MeetingInfo>({
  meetingTime: undefined,
  meetingLocation: '',
  chairperson: '',
  meetingType: 'regular',
  participants: [],
  agenda: '',
  minutes: ''
})

// 表单验证规则
const formRules = {
  meetingTime: [{ required: true, message: '请选择会议时间' }],
  meetingLocation: [{ required: true, message: '请输入会议地点' }],
  chairperson: [{ required: true, message: '请选择会议主持人' }]
}

// ============================ 计算属性和方法 ============================

// 过滤选项
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}



// 监听props变化
watch(() => props.value, (newValue) => {
  if (newValue) {
    // 深拷贝并处理日期格式
    const processedValue = { ...newValue }

    // 确保meetingTime是dayjs对象或null
    processedValue.meetingTime = ensureDayjsObject(processedValue.meetingTime)

    Object.assign(formData, processedValue)
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:value', newValue)
  emit('change', newValue)
}, { deep: true })

// ============================ 事件处理 ============================

function handleValuesChange() {
  // 表单值变化时的处理
}

function handleSubmit() {
  emit('submit', formData)
}

function handleReset() {
  formRef.value?.resetFields()
}

function handleAddParticipant() {
  const newParticipant: Participant = {
    id: `temp_${Date.now()}`,
    userId: '',
    userName: '',
    role: '',
    department: '',
    expertise: '',
    contribution: ''
  }
  formData.participants.push(newParticipant)
}

function handleRemoveParticipant(index: number) {
  formData.participants.splice(index, 1)
}

function handleParticipantChange(index: number, field: string, value: any) {
  if (field === 'userId') {
    const user = userList.value.find(u => u.id === value)
    if (user) {
      formData.participants[index].userName = user.name
      formData.participants[index].department = user.department
    }
  }
}

function handleUserDropdown(open: boolean) {
  if (open && !userList.value.length) {
    loadUserList()
  }
}

async function loadUserList() {
  loadingUsers.value = true
  try {
    // 模拟加载用户列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    userList.value = [
      { id: 'user1', name: '张医生', department: '内科' },
      { id: 'user2', name: '李护士长', department: '护理部' },
      { id: 'user3', name: '王主任', department: '质控科' },
      { id: 'user4', name: '赵药师', department: '药剂科' }
    ]
  } catch (error) {
    message.error('加载用户列表失败')
  } finally {
    loadingUsers.value = false
  }
}

// ============================ 生命周期 ============================

onMounted(() => {
  // 初始化时加载用户列表
  loadUserList()
})
</script>

<style scoped lang="less">
.meeting-info-form {
  .form-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .empty-participants {
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    border-radius: 6px;
  }
  
  .participants-list {
    .participant-item {
      padding: 12px;
      margin-bottom: 8px;
      background-color: #fafafa;
      border-radius: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      :deep(.ant-form-item) {
        margin-bottom: 0;
      }
    }
  }
  
  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .meeting-info-form {
    .participants-list {
      .participant-item {
        :deep(.ant-row) {
          flex-direction: column;
          
          .ant-col {
            width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>
