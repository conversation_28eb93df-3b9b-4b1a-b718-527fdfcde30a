<script lang="ts" setup>
import { ref, unref, watch,onMounted,nextTick } from 'vue'
import { patient,schemas3,addTableData,addSeach,tableData1,tableData2,tableData3,tableData5,yachuangSchema,preTable} from './info.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm, FormSchema} from '@/components/Form'
import { Tag } from 'ant-design-vue';
import { PlusCircleOutlined, MinusCircleOutlined, ExpandOutlined } from '@ant-design/icons-vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import {
  findEvtname,deleteInfoPressureSore,getInfoPressureSorePage,createInfoPressureSore,updateInfoPressureSore,createInfoPatient,auditPersonPage,
  updateStatusInfo,getUserPage,findSysQuestions,getEvtcontent,createInfo, getInfo,updateInfoDiscussant,createInfoAssignment,updateInfoAssignment,getInfoAssignmentPage,batchSaveInfoContent, updateInfo,deleteInfoResponsiblePersons, getInfoPatient,getMeasuresTree,getEvtnameData,findTreeInfoCause,batchSaveMeasures,batchSaveInfoCause,getCauseTree,updateInfoPatient,updateBatchSave,getInfoDiscussantPage,updateInfoDiscussantSave,
  getUser,getInfoAssignment, getInfoResponsiblePersonsPage,deleteInfoAssignment,listSimpleDept,getInfoAbarbeitungPersons,updategetInfoAbarbeitungPersonsSave,deletegetInfoAbarbeitungPersons,findTreeInfoMeasures,deleteInfoDiscussant
} from '@/api/evt/info'
import { deleteFile, getFilePage } from '@/api/infra/file'
import { BasicTable, useRender, TableAction,BasicColumn,useTable } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict';
import { uploadApi } from '@/api/base/upload'
import { IconEnum } from '@/enums/appEnum'
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router'
import { getUserInfo } from '@/api/base/user'

import FishboneDialog from "@/components/fishbone/FishboneDialog.vue";
import FbData from '@/components/fishbone/data.json'

import type { FormInstance } from 'element-plus'
let $ = ""; //将go.js 方法定义为$

let evtType = ''
let userData = {}
const deptId = ref<number>()
onMounted(async () => {
  const routeName= useRoute().name
  const data = await getUserInfo()

  deptId.value = data.user.deptId
  userData = data.user
  // evtType
  if(routeName  == 'Info_1'){
    evtType = '1'
  }else if(routeName  == 'Info_2'){
    evtType = '2'
  }else if(routeName  == 'Info_3'){
    evtType = '3'
  }else{
    evtType = '4,5,6'
  }

  setTimeout(() => {
    if (sun.value) {
      sun.value.initFishDom();
      sun.value.getfishbone();
    }
  },100)
})

//表单内容
const preData = ref()
const preFlag = ref(false)
const preRef = ref()

const ypform = ref({})

const nameOptions = ref([])
const ops = ref()
async function getList(e) {
  const res = await getEvtnameData({pageSize:100,evtClass:e})
  nameOptions.value = res.list
  ops.value = nameOptions.value.map(item=>({value:item.id,label:item.evtName}))
}

//事件内容
const schemas1: FormSchema[] = [
  {
    field: 'evtClass',
    component: 'Select',
    required: true,
    label: '事件分类',
    colProps: {
      span: 12,
    },
    componentProps:{
      options: getDictOptions(DICT_TYPE.EVT_CLASS,'string'),
      disabled:true,
    }
  },
  {
    label: '事件名称',
    required: true,
    field: 'evtname',
    component: 'Input',
    colProps: {
      span: 24,
    },
    componentProps:{
      disabled:true,
    },
  },
  {
    label: '事件名称',
    required: true,
    field: 'evtnameId',
    show:false,
    component: 'ApiSelect',
    colProps: {
      span: 24,
    },
    componentProps:{
      options: ops,
      disabled:true,
    },
  },
  {
    field: 'evTime',
    component: 'DatePicker',
    required: true,
    label: '发生时间',
    colProps: {
      span: 24,
    },
    componentProps: {
      showTime:true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'x',
    }
  },
  {
    field: 'evDeptId',
    component: 'ApiTreeSelect',
    label: '发生科室',
    required: true,
    componentProps: {
      api: () => listSimpleDept(),
      handleTree:'id',
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'evtLevel',
    required: true,
    component: 'Select',
    label: '事件级别',
    colProps: {
      span: 12,
    },
    componentProps: {
      options: getDictOptions(DICT_TYPE.EVT_LEVEL,'string'),
    },
  },
  {
    field: 'evtCritical',
    component: 'Select',
    // required: true,
    label: '伤害程度',
    componentProps: {
      options: getDictOptions(DICT_TYPE.evt_pat_level,'string'),
    },
  },
]
const onAddItem = (element,options) => {
  let obj = {}
  element.tableData.push(obj)
}
const deleteRow = (scope,index: number,element) => {
  element.tableData.splice(index, 1)
}
//本地暂时存储
watch(preData,(val)=>{
  sessionStorage.setItem('preData',JSON.stringify(val))
},{deep: true})


// 深拷贝
const deepClone = (data) => {
  const type = getObjType(data)
  let obj
  if (type === 'array') {
    obj = []
  } else if (type === 'object') {
    obj = {}
  } else {
    //不再具有下一层次
    return data
  }
  if (type === 'array') {
    for (let i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (const key in data) {
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}
const getObjType = (obj) => {
  const toString = Object.prototype.toString
  const map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object',
  }
  if (obj instanceof Element) {
    return 'element'
  }
  return map[toString.call(obj)]
}
defineOptions({ name: 'InfoModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)
const isEvent = ref(false)
// const [registerForm, { setFieldsValue, resetFields, resetSchema, validate }] = useForm({
//   labelWidth: 120,
//   baseColProps: { span: 24 },
//   schemas: createFormSchema,
//   showActionButtonGroup: false,
//   actionColOptions: { span: 23 },
// })
const  [form2, {  validate:vdate2,resetFields:resetF2,resetSchema:resetS2,updateSchema:update2,setFieldsValue:setValue2 }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: schemas1,
  showActionButtonGroup: false,
  actionColOptions: { span: 24 },
})
const [form1, { validate:vdate1,submit:sub1 }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: patient,
  actionColOptions: { span: 24 },
  showActionButtonGroup: false,
})
const  [form3, {  validate:vdate3,resetFields:resetF3 }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: schemas3,
  showActionButtonGroup: false,
  actionColOptions:{ span: 24 }
})
const evtData: FormSchema[] = [
  {
    field: 'disTime',
    component: 'DatePicker',
    label: '参会时间',
    colProps: {
      span: 12,
    },
    componentProps: {
      showTime:true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'x',
      onChange:async (e)=>{
        let obj={
          disTime:e,
          evtcontentId:itemData.evtcontentId,
          id:itemId
        }

        await updateInfo(obj)
      }
     }
  },
  {
    field: 'venue',
    component: 'ApiTreeSelect',
    label: '参会地点',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => listSimpleDept(),
      handleTree: 'id',
      onChange:async (e)=>{
        let obj={
          venue:e,
          evtcontentId:itemData.evtcontentId,
          id:itemId
        }
        await updateInfo(obj)
      }
    },
  },
]

async function onBlur(e){
  let obj={
    sumnotes:e.target.value,
    evtcontentId:itemData.evtcontentId,
    id:itemId
  }
  await updateInfo(obj)
}
const  [form4, {  getFieldsValue:getV4, }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: evtData,
  showActionButtonGroup: false,
  actionColOptions:{ span: 24 }
})

let itemId = ''
let itemData = {}
const itemStatus = ref(false)
const isYaoPin = ref(false)
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false,maskClosable:false, })
  activeKey.value = ['1']
  isEvent.value = !!data?.isEvent
  if(data.record && data.record.status == '1'){
    itemStatus.value = true
  }else{
    itemStatus.value = false
  }
  if(data.record && data.record.status == '5'){
    itemStatus.value = true
  }
  if(data.record && data.record.status == '3'){
    itemStatus.value = true
  }
  if (unref(isEvent)) {
    itemId = data.record.id
    itemData = data.record
    setModalProps({ title: `事件上报（${itemData.evtname} & ${itemData.id}）`})

    //清除文件列表
    fileList.value = []
    YPfileList.value = []
    YCfileList.value = []
    // 患者信息
    // resetSchema(patient)
    const res1 = await getInfoPatient(itemId)
    patientdata.value = res1
    ypform.value = res1
    if(res1 == null){
      ypform.value = {}
    }

    if(patientdata.value == null){
      addflag.value = false
    }else{
      addflag.value = true
    }

    //事件内容 上报人信息
    const res3 = await getInfo(itemId)
    formdata1.value = res3
    resetS2(schemas1)
    formdata3.value = res3
    formdata3.value.repDeptId = deptId.value

    if(formdata3.value.attachments == null || formdata3.value.attachments == ''){
      formdata3.value.attachments = []
    }else{
      formdata3.value.attachments = formdata3.value.attachments.split(',')
      fileList.value = formdata3.value.fileList
    }


    await getEvtcontent(data.record.evtcontentId).then(res=>{
      if(/国家/.test(itemData.evtname) && /药品/.test(itemData.evtname)){
        isYaoPin.value = true
      }else{
        isYaoPin.value = false
      }

      preData.value = res

      preData.value.forEach(item=>{
        if(item.type == '2'){
          if(item.value == null){
            item.value = []
          }else{
            item.value = item.value.split(',')
          }
        }else if(item.type == '9'){
          // 🔧 修复表格数据处理逻辑：后端已经在tableData字段中返回了正确的表格数据
          if(item.tableData == null){
            item.tableData = []
          }
          // tableData字段已经是正确的格式，无需额外转换
        }
      })
      preFlag.value = true
    })


    const res6 = await findSysQuestions({infoId:itemId})
    setTimeout(()=>{
      if(res6.length){
        res6.forEach(item=>{
          if(item.type == '2'){
            if(item.value == null){
              item.value = []
            }else{
              item.value = item.value.split(',')
            }
          }else if(item.type == '6'){
            if(item.value !== null){
              item.value = parseInt(item.value)
            }
          }else{
            item.value = item.value
          }
          if(item.type == '8' && item.value ){
            getFilePage({ids:item.value}).then(res=>{
              YPfileList.value = res.list
            })
          }
          if(preData.value){
            preData.value.forEach(it=>{
              if(item.id == it.id){
                it.value = item.value
              }
              if(item.id == it.id && item.type == '9'){
                it.tableData = item.tableData
              }
            })
          }
        })

        preFlag.value = true
      }
    },500)

    // 对策知识库
    const res4 = await findTreeInfoCause({infoId:itemId})
    if(res4){
      treeLeft.value = res4.treeData
      huixianId = res4.nodeIds
    }

    //措施知识库
    const res5 = await findTreeInfoMeasures({infoId:itemId})
    if(res5){
      treeRight.value = res5.treeData
    }


  }else{
    itemStatus.value = false
    isEvent.value = false;

    if(data.rePort){
      addModel.value = false;
      auditModal.value = true
      itemData = data.record
      auditReload()
    }else{
      addModel.value = true;
    }

    fileList.value = [];
    YPfileList.value = [];
    YCfileList.value = [];
  }
})

//提交仅更改状态
async function handleSubmit() {
  try {
    if(isYaoPin.value == true){
      setTimeout(async ()=>{
        activeKey.value = ['1']
        const values2 = await vdate2()

        saveForm1('1')
        if(values2){
         setModalProps({ confirmLoading: true })
         await updateStatusInfo({id:itemId,status:'1'})
         closeModal()
         emit('success')
         createMessage.success(t('common.saveSuccessText'))
       }
      },50)
    }else{
      setTimeout(async ()=>{
        activeKey.value = ['1','2','4']
        const values2 = await vdate2()
        const values1 = await vdate1()
        const values3 = await vdate3()

        await saveForm1('1')
        await saveForm2('1')
        await saveForm3('1')
        if(values1 && values2 && values3){
         setModalProps({ confirmLoading: true })
         await updateStatusInfo({id:itemId,status:'1'})
         closeModal()
         emit('success')
         createMessage.success(t('common.saveSuccessText'))
       }
      },50)
    }

  } finally {
    setModalProps({ confirmLoading: false })
  }
}

//左侧锚点
const items = ref([
  {
    key: '1',
    href: '#p1',
    title: '事件内容',
  },
  {
    key: '2',
    href: '#p2',
    title: '患者信息',
  },
  {
    key: '3',
    href: '#p3',
    title: '责任人信息',
  },
  {
    key: '4',
    href: '#p4',
    title: '上报人信息',
  },

])

const activeKey = ref(['1']);
const activeKeyRight = ref(['1']);

const formRef1 = ref();
const formRef2 = ref();
const formRef4 = ref();
const formRef5 = ref();
const patientdata = ref<any>({});//查询事件上报内容明细列表
const addflag = ref(false)

async function saveForm1 (flag){
  const values = await vdate1()
  values.infoId = itemId

  if(addflag.value === false){
    await createInfoPatient(values).then(res=>{
      if(flag !== '1'){
        message.success('成功');
      }
      addflag.value = true
    })
  }else{
    updateInfoPatient(values).then(res=>{
      if(res == true){
        if(flag !== '1'){
          message.success('成功');
        }
      }
    })
  }
}
const formdata1 = ref<any>({});//事件内容

async function saveForm2 (flag){
  //事件内容
  const values = await vdate2()
  values.evtType = evtType
  values.evtClass = itemData.evtClass
  values.orgId = deptId.value
  values.status = '0'
  values.evtcontentId = itemData.evtcontentId
  values.id = itemId
  values.sumnotes = formdata3.value.sumnotes
  values.venue = getV4().venue
  values.disTime = getV4().disTime

  await updateInfo(values)
  // createMessage.success(t('common.saveSuccessText'))

  ypform.value.infoId = itemId
  if(isYaoPin.value == true){
    if(ypform.value.patCometype && ypform.value.patHosnumber && ypform.value.patName && ypform.value.patNewDay && ypform.value.patNewMonth && ypform.value.patNewYear && ypform.value.patSex ){
      if(addflag.value === false){
        await createInfoPatient(ypform.value).then(res=>{
          // message.success('成功');
          addflag.value = true
        })
      }else{
        updateInfoPatient(ypform.value).then(res=>{
          if(res == true){
            if(flag !== '1'){
              message.success('成功');
            }
          }
        })
      }
    }else{
      message.error('请填写必填项')
      return
    }
  }

  // const submitForm = (formEl: FormInstance | undefined) => {
  //   if (!formEl) return
  //   preRef.validate((valid) => {
  //     if (valid) {
  //       console.log('submit!')
  //     } else {
  //       console.log('error submit!')
  //     }
  //   })
  // }

  //表单内容
  let obj = {
    infoContentSaveReqVO:deepClone(preData.value)
  }

  obj.infoContentSaveReqVO.forEach(item=>{
    item.infoId = itemId
    if(item.type == '2'){
      if(item.value.length == 0){
        item.value = ''
      }else{
        item.value = item.value.join(',')
      }
    }
    if(item.type == '8'){
      if(item.value.length == 0 || item.value == null){
        item.value = ''
      }else{
        item.value = item.value.join(',')
      }
    }
  })

  if(obj.infoContentSaveReqVO){
    await batchSaveInfoContent(obj.infoContentSaveReqVO)
  }

  if(flag !== '1'){
    message.success('成功');
  }

}
async function saveForm3 (flag){
  const values = await vdate3()
  values.evtType = evtType
  values.evtClass = itemData.evtClass
  values.orgId = formdata3.value.repDeptId
  values.status = 0;
  values.id = itemId;
  console.log(formdata3.value);

  await updateInfo(values)
  if(flag !== '1'){
    message.success('成功');
  }
  // if(formdata3.value == null){
  //   await createInfo(values)
  //   message.success(t('common.saveSuccessText'))
  // }else{
  // }
}

const formdata3 = ref<any>({});//上报人信息

const containerRef = ref<HTMLElement | null>(null)
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
const rightRef = ref<HTMLElement | null>(null)


//原因对策分析
const modelOne = ref(false)
const modelTwo = ref(false)
const modelForm = ref({})
var huixianId = []
//tree组件
const treeData = ref<TreeProps['treeData']>([]);
const treeRef1 = ref()
const treeRef2 = ref()
const treeRef3 = ref()
const treeLeftRef = ref()
const treeRightRef = ref()

const treeLeft = ref()//外面tree数据
const treeRight = ref()
const treeModelTitle = ref()

const strictlyFlag = ref(true)//严格模式 影响勾选
// const selectedId = ref()
const checkArr = ref<Array[]>([])

//打开原因选择
async function handOpen(title,nodeId){
  treeData3.value = []
  strictlyFlag.value = true;

  treeModelTitle.value = title
  modelOne.value = true
  if(title == '原因选择'){
    //原因知识库
    await getCauseTree({ parentId: 0 }).then(res => {
      treeData.value = res

      strictlyFlag.value = false;
      treeRef1.value.setCheckedKeys(huixianId)
      treeData3.value = treeLeft.value
    })

  }else{
    await getMeasuresTree({ parentId: 0 }).then(res => {
      treeData2.value = res
    })
    // const res = await getCMTree({causeId:nodeId})
    await findTreeInfoMeasures({infoId:itemId,causeId:nodeId}).then(res=>{

      if(res.treeData){
        strictlyFlag.value = false;
        treeRef2.value.setCheckedKeys(res.nodeIds)
        treeData3.value = res.treeData
      }
    })

  }
}
//原因确定
async function handleOkTree(){
  if(treeModelTitle.value == '原因选择'){
    // treeLeft.value = treeData3.value
    let obj={
      infoCauseSaveReqVOs:checkArr.value,
    }
    await batchSaveInfoCause(obj.infoCauseSaveReqVOs).then(async res=>{
      message.success('成功')
      modelOne.value = false;
      //更新
      const res4 = await findTreeInfoCause({infoId:itemId})
      if(res4){
        treeLeft.value = res4.treeData
        huixianId = res4.nodeIds
      }
      treeRef2.value.setCheckedKeys([])
    })

  }else{
    // treeRight.value = treeData3.value
    // checkArr.value.forEach(item=>{
    //   item.causeId=causeData.value.causeId
    // })
    let obj={
      infoCauseSaveReqVOs:checkArr.value,
    }

    await batchSaveMeasures(obj.infoCauseSaveReqVOs).then(async res=>{
      message.success('成功')
      //更新
      const res5 = await findTreeInfoMeasures({infoId:itemId})
      if(res5){
        treeRight.value = res5.treeData
      }
      modelOne.value = false;
      treeRef2.value.setCheckedKeys([])
    })
  }
}
//临时增加
const formRef22 = ref()
function arrChild(data){
  let linshi = {
    ...modelForm.value,
  }
  data.forEach(item=>{
    if(item.id == modelForm.value.parentId){
      item.children.push(linshi)
      return
    }else{
      if(item.children.length){
        arrChild(item.children)
      }
    }
  })
}
function handOk(){
  formRef22.value.validate().then(res=>{
    let linshi = {
      ...modelForm.value,
    }
    arrChild(treeData3.value)

    checkArr.value.push(linshi)
    modelTwo.value = false;
    formRef22.value.resetFields()
  })

}
const handleClickp = (selectedKeys: string[], node: any) => {
  modelForm.value.parentId = selectedKeys.id;
  modelForm.value.isTemp = '1';
  modelForm.value.children = [];
  modelForm.value.infoId = itemId;
};


const arrayToTree = (Array) => {
  const result = [];   // 存放结果集
  const itemMap = {};  //
  for (const item of Array) {
    const id = item.id;
    const parentId = item.parentId;

    if (!itemMap[id]) {
      itemMap[id] = {
        children: [],
      }
    }
    itemMap[id] = { ...item, children: itemMap[id]['children'] }

    const treeItem = itemMap[id];

    if (parentId === 0) {
      result.push(treeItem);
    } else {
      if (!itemMap[parentId]) {
        itemMap[parentId] = {
          children: [],
        }
      }
      itemMap[parentId].children.push(treeItem)
    }

  }
  return result;

}

const onCheck = (data: string[], e: any) => {
  let ref = treeRef1
  if(treeModelTitle.value == '原因选择'){
    ref = treeRef1
  }else{
    ref = treeRef2
  }
  //父节点是否选中
  const isChecked = ref.value.getNode(data).checked
  const allChecked = ref.value.getCheckedNodes(false,true)
  let arr = []
  allChecked.forEach(item=>{
    let obj = {
      id:item.id,
      parentId:item.parentId,
      sort:item.sort,
      infoId:itemId,
      isTemp:'0',
    }
    if(treeModelTitle.value=='原因选择'){
      obj.causeName = item.causeName
    }else{
      obj.measuresName = item.measuresName
    }
    arr.push(obj)
  })
  checkArr.value = arr

  if (isChecked) {
    //选中
    treeData3.value = arrayToTree(ref.value.getCheckedNodes())
  } else {
    //取消选中
    treeData3.value = arrayToTree(ref.value.getCheckedNodes(false, true))
  }
  if (e.checkedNodes.length || data.children.length) {
    const pArr = ref.value.getHalfCheckedNodes()
    const item = {}
    const newArr = []
    if(pArr.length == 0){

    }else{
      pArr.forEach((e, i) => {
        item.parentId = 0;
        item.children = [];
        item.id = e.id;
        if(!treeModelTitle.value=='原因选择'){
          item.causeName = e.causeName
        }else{
          item.measuresName = e.measuresName
        }
        newArr.push(e)
      })

    }
    e.checkedNodes.push(...newArr)

    treeData3.value = arrayToTree(e.checkedNodes)
    strictlyFlag.value = false;
  }

};
//tree2
const treeData2 = ref<TreeProps['treeData']>([]);

const prop1 = {
  label: 'causeName',
}
const filterText1 = ref('')
watch(filterText1, (val) => {
  treeRef1.value!.filter(val)
})
const filterTextLeft = ref('')
watch(filterTextLeft, (val) => {
  treeLeftRef.value!.filter(val)
})
const filterTextRight = ref('')
watch(filterTextRight, (val) => {
  treeRightRef.value!.filter(val)
})

const filterNode1 = (value: string, data: Tree) => {
  if (!value) return true
  return data.causeName.includes(value)
}

const prop2 = {
  label: 'measuresName',
}

const filterText2 = ref('')
watch(filterText2, (val) => {
  treeRef2.value!.filter(val)
})

const filterNode2 = (value: string, data: Tree) => {
  if (!value) return true
  return data.measuresName.includes(value)
}

const treeData3 = ref<TreeProps['treeData']>([]);

const openModel = ref<any>(false)
const tableSaveSelect = ref<any>('')
function openSelect(title: string) {
  openModel.value = true
  tableSaveSelect.value = title
  setTimeout(()=>{
    clearSelectedRowKeys()
  },50)
  if(title === '人员选择'){
    let rowArr = []
    if(getData1().length){
      getData1().forEach(item=>{
        rowArr.push(Number(item.userId))
      })
      setTimeout(()=>{
        setSelectedRowKeys(rowArr)
      },50)
    }
  }else if(title === '参会人员选择'){
    let rowArr = []
    if(getData2().length){
      getData2().forEach(item=>{
        rowArr.push(Number(item.userId))
      })
      setTimeout(()=>{
        setSelectedRowKeys(rowArr)
      },50)
    }
  }else if(title === '责任人选择'){
    let rowArr = []
    if(getData3().length){
      getData3().forEach(item=>{
        rowArr.push(Number(item.userId))
      })
      setTimeout(()=>{
        setSelectedRowKeys(rowArr)
      },50)
    }
  }
}
//table人员选择按钮
async function handleOk(){
  if(getSelectRows().length == 0){
    return
  }
  if(tableSaveSelect.value === '人员选择'){
    getSelectRows()
    let obj = getSelectRows()
    obj.forEach(item=>{
      item.hljb = item.nurseLevel
      item.infoId = itemId
      item.userId = item.id
    })
    const res = await updateBatchSave(obj)
    if(res === true){
      createMessage.success(t('common.saveSuccessText'))
    }
    reload1()
    openModel.value = false;
  }else if(tableSaveSelect.value === '参会人员选择'){

    getSelectRows()
    let obj = getSelectRows()
    obj.forEach(item=>{
      item.infoId = itemId
      item.userId = item.id
      item.userName = item.nickname
    })

    const res = await updateInfoDiscussantSave(obj)
    if(res === true){
      createMessage.success(t('common.saveSuccessText'))
    }
    reload2()
    openModel.value = false;
  }else if(tableSaveSelect.value === '责任人选择'){
    getSelectRows()
    let obj = getSelectRows()
    obj.forEach(item=>{
      item.infoId = itemId
      item.userId = item.id
      item.userName = item.nickname
    })
    const res = await updategetInfoAbarbeitungPersonsSave(obj)
    if(res === true){
      createMessage.success(t('common.saveSuccessText'))
    }
    reload3()
    openModel.value = false;
  }
}


async function handleDelete(record: Recordable,title) {
  // await deleteInfo(record.id)
  // createMessage.success(t('common.delSuccessText'))
  // reload()

  if(title === '人员选择'){
    await deleteInfoResponsiblePersons(record.id)
    createMessage.success(t('common.delSuccessText'))
    reload1()
  }else if(title === '参会人员选择'){
    await deleteInfoDiscussant(record.id)
    createMessage.success(t('common.delSuccessText'))
    reload2()
  }else if(title === '责任人选择'){
    await deletegetInfoAbarbeitungPersons(record.id)
    createMessage.success(t('common.delSuccessText'))
    reload3()
  }else if(title === '措施整改'){
    await deleteInfoAssignment(record.id)
    createMessage.success(t('common.delSuccessText'))
    reload5()
  }else if(title === '部位'){
    await deleteInfoPressureSore(record.id)
    createMessage.success(t('common.delSuccessText'))
    reloadPre()
  }
}
const modalData: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'nickname',
    width: 160,
  },
  {
    title: '科室',
    dataIndex: 'deptName',
    width: 160,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 140,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.SYSTEM_USER_SEX)
    },
  },
]
const searchSchema: FormSchema[] = [
  {
    field: 'deptId',
    component: 'ApiSelect',
    label: '部门',
    componentProps: {
      api: () => listSimpleDept(),
      labelField:'name',
      valueField:'id',
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'nickname',
    component: 'Input',
    label: '姓名',
    componentProps: {
      placeholder: '请输入',
    },
    colProps: {
      span: 12,
    },
  },
]

//可编辑表格配置 方法等
// const currentEditKeyRef = ref('');

// function createActions(record: EditRecordRow): ActionItem[] {
//   if (!record.editable) {
//     return [
//       {
//         label: '编辑',
//         disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
//         onClick: handleEdit.bind(null, record),
//       },
//     ];
//   }
//   return [
//     {
//       label: '保存',
//       onClick: handleSave.bind(null, record),
//     },
//     {
//       label: '取消',
//       popConfirm: {
//         title: '是否取消编辑',
//         confirm: handleCancel.bind(null, record),
//       },
//     },
//   ];
// }
// table
const [addTable, { reload:addReload }] = useTable({
  maxHeight:500,
  beforeFetch:(parms) => { parms.evtType = evtType },
  api:findEvtname,
  columns:addTableData,
  // handleSearchInfoFn:(info)=>{

  // },
  formConfig: { labelWidth: 120, schemas: addSeach },
  useSearchForm: true,
  pagination:{ pageSize: 99 },
  showIndexColumn:false,
  // ellipsis:false,
  // scroll:{x:300,y:300},
  // isCanResizeParent:true,
  // canResize:true,
})

const [rTable1, { reload:reload1,getDataSource:getData1 }] = useTable({
  maxHeight:500,
  beforeFetch:(parms) => {
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoResponsiblePersonsPage,
  columns:tableData1,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  tableSetting: { fullScreen: true },
  actionColumn: {
    width: 100,
    title: t('common.action'),
    dataIndex: 'action',
    // slots: { customRender: 'action' },
  },
})
const [rTable2, { reload:reload2,getDataSource:getData2 }] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoDiscussantPage,
  maxHeight:500,
  rowClassName:getRowClassName,
  columns:tableData2,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  tableSetting: { fullScreen: true },
  actionColumn: {
    width: 100,
    title: t('common.action'),
    dataIndex: 'action',
    // slots: { customRender: 'action' },
  },
})
// const [rTable3, { reload:reload3,getDataSource:getData3 }] = useTable({
//   beforeFetch:(parms)=>{
//     parms.infoId = itemId
//     if(!parms.infoId){
//       parms.infoId = '0'
//     }else{
//       parms.infoId = itemId
//     }
//   },
//   api:getInfoAbarbeitungPersons,
//   maxHeight:500,
//   columns:tableData3,
//   pagination:{ pageSize: 10 },
//   showIndexColumn:false,
//   actionColumn: {
//     width: 100,
//     title: t('common.action'),
//     dataIndex: 'action',
//     // slots: { customRender: 'action' },
//   },
// })

const [rTable5, { reload:reload5}] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoAssignmentPage,
  maxHeight:500,
  columns:tableData5,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  actionColumn: {
    width: 180,
    title: t('common.action'),
    dataIndex: 'action',
    // slots: { customRender: 'action' },
  },
})

const [modelTable, { reload,getSelectRows,clearSelectedRowKeys,setSelectedRowKeys,getSelectRowKeys }] = useTable({
  title: '',
  // beforeFetch:(parms)=>{
  //   parms.deptId = itemData.repDeptId
  // },
  api: getUser,
  columns:modalData,
  maxHeight:500,
  formConfig: { labelWidth: 120, schemas: searchSchema },
  pagination:{ pageSize: 50 },
  pagination:false,
  useSearchForm: true,
  showTableSetting: false,
  showIndexColumn:false,
  rowKey:"id",
  rowSelection:{ type: 'checkbox' },
  // actionColumn: {
  //   width: 120,
  //   title: t('common.action'),
  //   dataIndex: 'action',
  //   fixed: 'right',
  // },
})

// 参会上传
const fileList = ref([])
const fileData = ref({})
function beforUpload(file) {
  fileData.value.filename = file.name
}
async function handleChange (file,files) {
  let arr = []

  files.forEach(async item=>{
    if(item.status == 'success'){
      if(item.response){
        arr.push(item.response.data.data)
      }else{
        arr.push(item.id)
      }
      formdata3.value.attachments = arr
    }
  })
  if(file.status == 'success'){
    let obj = {
      ids:formdata3.value.attachments.join(',')
    }
    await updateInfo({id:itemData.id,attachments:obj.ids})
    const res = await getFilePage(obj)
    fileList.value = res.list
    createMessage.info(`已上传文件`)
  }

}
function downFile (e) {
  if(e.status == "success"){
    var a = document.createElement("a");
    var event = new MouseEvent("click");
    a.target = "_blank";
    a.download = e.name;
    a.href = e.url;
    a.dispatchEvent(event);
  }
}
async function removeFile (file,files) {
  fileList.value = files
  let ids = []
  files.forEach(item=>{
    ids.push(item.id)
  })
  await updateInfo({id:itemData.id,attachments:ids.join(',')})
}
//药品上传
const YPfileList = ref([])
async function YPhandleChange (file,files) {
  let arr = []
  files.forEach(async item=>{
    if(item.status == 'success'){
      arr.push(item.response.data.data)
    }else{
      arr.push(item.id)
    }
    preData.value[35].value = arr
  })
  if(file.status == 'success'){
    let obj = {
      ids:preData.value[35].value.join(',')
    }
    const res = await getFilePage(obj)
    YPfileList.value = res.list
    createMessage.info(`已上传文件`)
  }


}
// function YPdownFile (e) {
//   if(e.status == "success"){
//     var a = document.createElement("a");
//     var event = new MouseEvent("click");
//     a.target = "_blank";
//     a.download = e.name;
//     a.href = e.url;
//     a.dispatchEvent(event);
//   }
// }
function YPremoveFile (file,files) {
  YPfileList.value = files
  let arr = []
  console.log(YPfileList.value);

  YPfileList.value.forEach(async item=>{
    if(item.id){
      arr.push(item.id)
      preData.value[35].value = arr
    }else if(item.response.data.data){
      arr.push(item.response.data.data)
      preData.value[35].value = arr
    }
  })
}
//压疮上传
const ycData = ref({})
function hanldData(params:type) {
    ycData.value = params
}
const YCfileList = ref([])
function YChandleChange (file,files) {
  files.forEach(async item=>{
    if(item.status == 'success'){
      const res = await getFilePage({ids:file.response.data.data})
      YCfileList.value = res.list
      ycData.value.fileName = YCfileList.value[0].name
      ycData.value.fileUrl = YCfileList.value[0].url
      const save = updateInfoPressureSore({...ycData.value,infoId:itemData.id})
    }
  })
  if(file.status == 'success'){
    createMessage.info(`已上传文件`)
  }

}
function fileDown (e) {
  if(e.fileUrl){
    var a = document.createElement("a");
    var event = new MouseEvent("click");
    a.target = "_blank";
    a.download = e.fileName;
    a.href = e.fileUrl;
    a.dispatchEvent(event);
  }
}
function YCremoveFile (file,files) {
  YCfileList.value = files
}

const rowClassName = ref()
function rowClick(record, index, event){
  if(itemData.status == '5'){
    return
  }
  record.isAbsent = '1';
  updateInfoDiscussant({...record}).then(res=>{
    message.success('成功')
    reload2()
  })
  rowClassName.value = index;
}
function getRowClassName(record, index) {
    return record.isAbsent == 1 ? "tableRowStyle" : "";
}

//找tree最后一级
function getLastLevelNodes(data) {

  let lastLevelNodes = [];
  data.forEach(item => {
    if (item.children == 0) {
      lastLevelNodes.push(item);
    } else {
      lastLevelNodes = lastLevelNodes.concat(getLastLevelNodes(item.children));
    }
  });

  return data = lastLevelNodes;
}
const oData = ref([])
const modelThree = ref(false)
const modelUpdate = ref(false)
const modelItemData = ref()
const dontEdit = ref(false)
//整改表单
const threeForm: FormSchema[] = [
  {
    field: 'measuresId',
    component: 'Select',
    label: '对策措施',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      options: oData.value,
    },
  },
  {
    field: 'measuresName',
    component: 'Input',
    label: '对策措施Name',
    show:false,
  },
  {
    label: '整改部门',
    field: 'abaDeptid',
    required: true,
    component: 'ApiTreeSelect',
    componentProps: ({ formModel ,formActionType})=>{
      return{
      api: () => listSimpleDept(),
      handleTree: 'id',
        onChange:async (e,label)=>{
          formModel.abaUserid = null
          formModel.abaDeptname = label.join(',')
          const res = await getUserPage({deptId:e,pageSize:99})
          // res.list.map(item=>({label:item.realname,value:item.id}))
          formActionType.updateSchema(
            {
              field: 'abaUserid',
              component: 'Select',
              label: '整改负责人',
              colProps: { span: 24 },
              required: true,
              componentProps: ({ formModel ,formActionType})=>{
                return{
                  options: res.list.map(item=>({label:item.nickname,value:item.id})),
                  onChange:(e)=>{
                    formModel.abaUsername = res.list.find(item=>item.id == e).nickname
                  }
                }
              },
            },
          )

        },
      }
    },
  },
  {
    field: 'abaDeptname',
    component: 'Input',
    label: '整改部门名称',
    show:false,
  },
  {
    field: 'abaUserid',
    component: 'Select',
    label: '整改负责人',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'abaUsername',
    component: 'Input',
    label: '整改负责人名称',
    show:false,
  },
  {
    field: '[abaStarttime, abaEndtime]',
    label: '整改时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['整改开始时间', '整改结束时间'],
      // showTime: { format: 'HH:mm:ss' },
    },
  },
  {
    label: '评估科室',
    field: 'evaDeptid',
    required: true,
    component: 'ApiTreeSelect',
    componentProps: ({ formModel ,formActionType})=>{
      return{
      api: () => listSimpleDept(),
      handleTree: 'id',
        onChange:async (e,label)=>{
          formModel.evaUserid = null
          formModel.evaDeptname = label.join(',')
          const res = await getUserPage({deptId:e,pageSize:99})
          formActionType.updateSchema(
            {
              field: 'evaUserid',
              component: 'Select',
              label: '评估人',
              colProps: { span: 24 },
              required: true,
              componentProps: ({ formModel ,formActionType})=>{
                return{
                  options: res.list.map(item=>({label:item.nickname,value:item.id})),
                  onChange:(e)=>{
                    formModel.evaUsername = res.list.find(item=>item.id == e).nickname
                  }
                }
              },
            }
          )
        },
      }
    },
  },
  {
    field: 'evaDeptname',
    component: 'Input',
    label: '评估科室名称',
    show:false,
  },
  {
    field: 'evaUserid',
    component: 'Select',
    label: '评估人',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'evaUsername',
    component: 'Input',
    label: '评估人名称',
    show:false,
  },
  {
    field: '[evaStarttime, evaEndtime]',
    label: '评估时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['评估开始时间', '评估结束时间'],
      // showTime: { format: 'HH:mm:ss' },
    },
  },
]
const upDatethreeForm: FormSchema[] = [
  {
    field: 'measuresName',
    component: 'Input',
    label: '对策措施',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      disabled:true,
      readonly:true,
    },
  },
  {
    field: '[abaStarttime, abaEndtime]',
    label: '整改时间',
    component: 'RangePicker',
    componentProps: {
      disabled:true,
      readonly:true,
      format: 'YYYY-MM-DD',
      placeholder: ['整改开始时间', '整改结束时间'],
    },
  },
  {
    field: 'abaActualEndtime',
    component: 'DatePicker',
    required: true,
    label: '实际整改时间',
    colProps: {
      span: 24,
    },
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    }
  },
  {
    field: 'abaStatus',
    component: 'RadioGroup',
    label: '完成情况',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      options: [
        {value:'1',label:'整改已完成'},
        {value:'0',label:'持续整改'}
      ],
    },
  },
  {
    label: '上传附件',
    field: 'abaFilename',
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      // fileType: "['.doc','.docx']",
      onchange(e){
        console.log(e);

      }
    },
  },
  {
    field: 'abaDescription',
    component: 'InputTextArea',
    label: '整改情况描述',
    colProps: { span: 24 },
  },

]
const pjForm: FormSchema[] = [
  {
    field: 'measuresName',
    component: 'Input',
    label: '对策措施',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      disabled:true,
      readonly:true,
    },
  },
  {
    field: '[abaStarttime, abaEndtime]',
    label: '整改时间',
    component: 'RangePicker',
    componentProps: {
      disabled:true,
      readonly:true,
      format: 'YYYY-MM-DD',
      placeholder: ['整改开始时间', '整改结束时间'],
    },
  },
  {
    field: 'abaActualEndtime',
    component: 'DatePicker',
    required: true,
    label: '实际整改时间',
    colProps: {
      span: 24,
    },
    componentProps: {
      disabled:true,
      readonly:true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    }
  },
  {
    field: '[evaStarttime, evaEndtime]',
    label: '评估时间',
    component: 'RangePicker',
    componentProps: {
      disabled:true,
      readonly:true,
      format: 'YYYY-MM-DD',
      placeholder: ['评估开始时间', '评估结束时间'],
    },
  },
  {
    field: 'abaDescription',
    component: 'InputTextArea',
    label: '整改情况描述',
    colProps: { span: 24 },
    componentProps: {
      disabled:true,
      readonly:true,
    }
  },
  {
    field: 'evaActualEndtime',
    component: 'DatePicker',
    label: '评估实际结束时间',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    }
  },
  {
    field: 'evaStatus',
    component: 'RadioGroup',
    label: '评价情况',
    colProps: { span: 24 },
    required: true,
    componentProps: {
      options: [
        {value:'1',label:'评价已完成'},
        {value:'0',label:'持续整改'}
      ],
    },
  },
  {
    label: '上传附件',
    field: 'evaFilename',
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: "['.doc','.docx']",
    },
  },
  {
    field: 'evaDescription',
    component: 'InputTextArea',
    label: '评估情况描述',
    colProps: { span: 24 },
  },

]

const [modelThreeForm, {  validate:vdateThree,resetFields:resetFThree,updateSchema:updateThreeSchema,resetSchema:resetModel,setFieldsValue:setModel}] = useForm({
  labelWidth: 140,
  baseColProps: { span: 24 },
  schemas: threeForm,
  showActionButtonGroup: false,
  actionColOptions:{ span: 24 }
})
function openModel3(){
  modelThree.value = true
  modelUpdate.value = false
  setTimeout(() => {
    resetFThree()
    resetModel(threeForm)
    oData.value = getLastLevelNodes(treeRight.value)

    updateThreeSchema(
      {
        field: 'measuresId',
        component: 'Select',
        label: '对策措施',
        colProps: { span: 24 },
        required: true,
        componentProps: ({ formModel ,formActionType})=>{
          return{
            options: oData.value.map(item=>({label:item.measuresName,value:item.measuresId})),
            onChange:(e)=>{
              formModel.measuresName = oData.value.find(item=>item.measuresId == e).measuresName
            }
          }
        },
      },
    )
  },100)
}

async function handleUpdate(data,flag){
  if(flag == '1'){
    //评价
    dontEdit.value = false
    modelUpdate.value = true
    modelThree.value = true
    setTimeout(async()=>{
      resetFThree()
      resetModel(pjForm)
      const res = await getInfoAssignment(data.id)
      modelItemData.value = res
      setModel({ ...res })
    },100)

  }else if(flag == '2'){
    dontEdit.value = true
    modelThree.value = true
    setTimeout(async()=>{
      resetFThree()
      resetModel(pjForm)
      const res = await getInfoAssignment(data.id)
      modelItemData.value = res
      setModel({ ...res })
    },100)
  }else{
    dontEdit.value = false
    modelUpdate.value = true
    modelThree.value = true
    setTimeout(async()=>{
      resetFThree()
      resetModel(upDatethreeForm)
      const res = await getInfoAssignment(data.id)
      modelItemData.value = res
      setModel({ ...res })
    },100)
  }

}
async function handOkThree(){
  if(modelUpdate.value == true){
    //确认整改
    const values = await vdateThree()
    values.infoId = itemId
    values.abaStarttime = new Date(values.abaStarttime).getTime()
    values.abaEndtime = new Date(values.abaEndtime).getTime()
    let obj = {
      ...modelItemData.value,
      ...values
    }
    console.log(obj);

    const res = await updateInfoAssignment(obj)
    if(res){
      message.success('成功')
      reload5()
    }
  }else{
    //新增
    const values = await vdateThree()
    values.infoId = itemId
    values.abaStarttime = new Date(values.abaStarttime).getTime()
    values.abaEndtime = new Date(values.abaEndtime).getTime()
    values.evaStarttime = new Date(values.evaStarttime).getTime()
    values.evaEndtime = new Date(values.evaEndtime).getTime()

    const res = await createInfoAssignment({...values})
    if(res){
      message.success('成功')
      reload5()
    }
  }
  modelThree.value = false
}


const addModel = ref(false)

async function handEvent(data){
  itemData = data
  itemStatus.value = false
  addModel.value = false
  isEvent.value = true;
  ypform.value = {};
  nextTick(()=>{
    resetF2()
    resetF3()
    setValue2({
      evtClass:data.evtClass,
      evtnameId:data.id,
      evtname:data.evtName,
    })
    formdata3.value.repDeptId = deptId.value
  })

  //新增上报
  const res = await createInfo({status:'0',evtType:data.evtType,orgId:deptId.value,evtClass:data.evtClass,evtnameId:data.id,evtname:data.evtName,evtcontentId:data.evtContentId})
  itemId = res
  setModalProps({ title: `事件上报（${itemData.evtName} & ${itemId}）`})

  isEvent.value = true

  const res1 = getInfoPatient('0')
  patientdata.value = res1



  //事件内容 上报人信息
  const res3 = await getInfo(itemId)
  formdata1.value = res3
  formdata3.value = res3
  formdata3.value.repDeptId = deptId.value
  formdata3.value.repWorkyear = userData.companyJoinAge
  formdata3.value.repAge = userData.age
  console.log(res3);

  const res4 = await findTreeInfoCause({infoId:'0'})
  if(res4){
    treeLeft.value = res4.treeData
    huixianId = res4.nodeIds
  }

  //措施知识库
  const res5 = await findTreeInfoMeasures({infoId:'0'})
  if(res5){
    treeRight.value = res5.treeData
  }

  getEvtcontent(data.evtContentId).then(res=>{
    if(/国家/.test(data.evtName) && /药品/.test(data.evtName)){
      isYaoPin.value = true
    }else{
      isYaoPin.value = false
    }
    preData.value = res
    preData.value.forEach(item=>{
      if(item.type == '2'){
        if(item.value == null){
          item.value = []
        }else{
          item.value = item.value.split(',')
        }
      }else if(item.type == '9'){
        if(item.value == null){
          item.tableData = []
        }else{
          // 🔧 修复表格数据处理逻辑：正确转换为tableData格式
          item.tableData = Array.isArray(item.value) ? item.value : []
        }
      }
    })
    preFlag.value = true
  })

}

function addCancel(){
  addModel.value = false
  closeModal()
}

function onClickfd(lr){
  let lefte = document.querySelector('.left')
  let righte = document.querySelector('.right')
  console.log(lefte,righte);
  if(lr =='left'){
    lefte.style.width = '90%'
    righte.style.width = '10%'
    activeKeyRight.value = []
  }else if(lr =='right'){
    lefte.style.width = '10%'
    righte.style.width = '90%'
    activeKey.value = []
  }else{
    lefte.style.width = '50%'
    righte.style.width = '50%'
  }
}

//压疮
const  [registerYc, {  validate:vdateYc,resetFields:resetYc,resetSchema:resetSYc,updateSchema:updateYC,setFieldsValue:setValueYc }] = useForm({
  labelWidth: 240,
  baseColProps: { span: 24 },
  schemas: yachuangSchema,
  // showActionButtonGroup: true,
  actionColOptions:{ span: 24 },
  labelAlign:'right',
  submitButtonOptions:{
    text:'提交',
  }
  // showResetButton:true,
  // showSubmitButton: true,
})

const modelFour = ref(false)
const ycForm = ref({})
const ycisAdd = ref(false)
let ycId = ''
//压疮提交到表格
async function ycSubmit(value){
  value.infoId = itemId

  console.log(value);
  if(ycisAdd.value){
    value.id = ycId
    const res = await updateInfoPressureSore(value)
    reloadPre()
    message.success('成功')
    modelFour.value = false;
  }else{
    const res = await createInfoPressureSore(value)
    reloadPre()
    message.success('成功')
    modelFour.value = false;
  }
}
//修改压疮
function preUpdate(record,type){
  modelFour.value = true;
  ycForm.value = record;
  if(type == 'add'){
    ycisAdd.value = false
    setTimeout(() => {
      resetYc()
    }, 50);
  }else{
    ycisAdd.value = true
    ycId = record.id
  }
  setTimeout(() => {
    setValueYc(record)
  }, 50);
}


const [preTableReg, { reload:reloadPre,getDataSource:getYcData }] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoPressureSorePage,
  title:"压力性损伤部位",
  maxHeight:400,
  columns:preTable,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  actionColumn: {
    width: 180,
    title: t('common.action'),
    dataIndex: 'action',
    // slots: { customRender: 'action' },
  },
})


//报告
const auditModal = ref()
const auditData: BasicColumn[] = [
  {
    title: '科室',
    dataIndex: 'auditDeptname',
    width: 160,
  },
  {
    title: '审核流程名称',
    dataIndex: 'auditName',
    width: 160,
    },
    {
      title: '审核人',
      dataIndex: 'auditUsername',
      width: 160,
    },
    {
      title: '审核结果',
      dataIndex: 'auditUsername',
      width: 160,
      customRender: ({ record, text }) => {
        return text == 0 ? '驳回' : text == 1 ? '通过' : '终止上报'
      },
    },
    {
      title: '审核描述',
      dataIndex: 'auditDescription',
      width: 200,
    },
]
const [auditTable, { reload:auditReload }] = useTable({
    title: '',
    beforeFetch:(parms)=>{
      parms.infoId = itemData.id
    },
    pagination:false,
    api: auditPersonPage,
    columns:auditData,
    maxHeight:400,
    formConfig: { labelWidth: 120, schemas: searchSchema },
    useSearchForm: false,
    showTableSetting: false,
    showIndexColumn:false,
})

//鱼骨图
const yuguModel = ref(false)
const sun = ref(null); //获取鱼骨图组件
let rjlhfList = ref([])
let fbData = ref(FbData) //自定义的鱼骨图数据来源 JSon

function canvasSize(type){
  if(sun.value){
    sun.value.changeScale(type)
  }
}
function addName(arr) {
  arr.forEach(item => {
    item.text = item.causeName
    item.name = item.causeName
    item.color = ""
    item.borColor = ""
    item.size = 18
    // item.weight = null
    // item.figure = null
    // item.isBorder = null
    // item.isReal = '1'
    // item.isTemp = null
    if (item.children) {
      addName(item.children);
    }
  });
  return arr
}

function openyugutu() {
  if(!treeLeft.value || treeLeft.value.length == 0){
    return
  }
  yuguModel.value = true;
  let arr = addName(treeLeft.value)
  fbData.value.children = treeLeft.value
  // itemData.evtname || itemData.evtName
  if(!fbData.value.children[fbData.value.children.length - 1].isYuwei){
    fbData.value.children.push({
      isYuwei:true,
      text:null,
      id:null,
      isBorder: null,
      color: "#555",
      borColor: "#e59955",
    })
  }
  setTimeout(() => {
    if (sun.value) {
      sun.value.initFishDom();
      sun.value.getfishbone();
    }
  },100)
}
</script>
<template>
  <div>
    <BasicModal  v-bind="$attrs" :title="isEvent ? `事件上报  （${itemData.evtname} & ${itemData.id}）` : '新增事件' "
      @register="registerModal" @ok="handleSubmit" :defaultFullscreen="true" :showOkBtn="!itemStatus" okText="提交" >
      <div v-if="isEvent" class="container1">
        <div class="left" id="container" ref="containerRef">
          <div style="width: 100%;">
            <el-affix :offset="60">
              <el-anchor :container="containerRef" type="default" :offset="30" direction="horizontal" @click="handleClick">
                <div v-for=" (it,index) in items" v-if="isYaoPin">
                  <el-anchor-link class="leftLabel" :href="it.href" :title="it.title" v-if="index<1" />
                </div>
                <div v-for=" (it,index) in items" v-else>
                  <el-anchor-link class="leftLabel" :href="it.href" :title="it.title" />
                </div>
              </el-anchor>
            </el-affix>
            <a-collapse v-model:activeKey="activeKey">
              <a-collapse-panel class="custom-panel" key="1" header="事件内容" id="p1">
                <BasicForm :schemas="schemas1" :model="formdata1" :disabled="itemStatus" @register="form2" ref="formRef2" />
                <!-- <div v-if="preFlag" style="display: flex;justify-content: space-between;margin-top: 10px;">
                  <p style="font-size: 16px;font-weight: bold;display: inline-block;">表单内容</p>
                  <a-button  type="primary" @click="clearForm">重置</a-button>
                </div> -->

                <el-form :disabled="itemStatus" v-if="preFlag && !isYaoPin" ref="preRef" style="max-width: 100%;padding: 10px;color: rgba(0, 0, 0, 0.88);" :model="preData" label-width="180" label-position="top">
                  <div v-for="(element, index) in preData" :key="index + 'b'" class="pre">
                    <el-form-item :label="element.name" :key="index" v-if="element.type == '1'"
                      :required="element.isRequire == '1' ? true : false" >
                      <el-radio-group v-model="element.value">
                        <el-radio v-for="(item, oIndex) in element.options" :key="item.id" :label="item.name" :value="item.id"
                          size="large"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="element.type == '2'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-checkbox-group v-model="element.value">
                        <el-checkbox v-for="(item) in element.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item v-if="element.type == '3'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-input v-model="element.value" style="width: 240px" placeholder="输入内容" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '4'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-input v-model="element.value" style="width: 240px" :autosize="{ minRows: 2, maxRows: 4 }"
                        type="textarea" placeholder="输入内容" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '5'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-select v-model="element.value" placeholder="请选择" style="width: 240px">
                        <el-option v-for="(item) in element.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item v-if="element.type == '6'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <!-- <el-input-number v-model="element.value" :min="element.min" :max="element.max"  @change="changeNum" /> -->
                      <a-input-number :disabled="itemStatus" v-model:value="element.value" :min="element.min" :max="element.max" style="min-width:140px;width: 40%;" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '7'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-date-picker v-model="element.value" type="datetime" placeholder="选择时间" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '9'" :label="element.name" :key="index"
                      :required="element.isRequire == '1' ? true : false">
                      <el-table :data="element.tableData" style="width: 100%" max-height="300">
                        <el-table-column v-for="(item, oIndex) in element.options" :key="oIndex" :prop="item.prop" :label="item.name">
                          <template #default="scope">
                            <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                          </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="120">
                          <template #default="scope">
                            <el-button
                              link
                              type="primary"
                              size="small"
                              @click.prevent="deleteRow(scope,scope.$index,element)"
                            >
                              删除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-button class="mt-4" style="width: 100%" @click="onAddItem(element,element.options)">
                        添加
                      </el-button>
                    </el-form-item>
                  </div>

                </el-form>

                <BasicTable v-if="itemData.isPressureSore == '0'" @register="preTableReg" >
                  <template #toolbar>
                    <a-button :disabled="itemData.status == '5' || itemData.status == '3' " type="primary" @click="preUpdate(null,'add')"> 添加 </a-button>
                  </template>
                  <template #bodyCell="{ column, record,text }">
                    <template v-if="column.key === 'file'">
                      <el-upload
                        v-model:file-list="YCfileList"
                        class="upload-demo"
                        :http-request="uploadApi"
                        :on-change="YChandleChange"
                        :on-preview='downFile'
                        :before-upload="beforUpload"
                        :on-remove="YCremoveFile"
                        :data="fileData"
                        :limit="1"
                        :disabled="itemData.status == '5' || itemData.status == '3'"
                        >
                        <!-- :disabled="itemData.status == '5'" -->
                        <el-button :disabled="itemData.status == '5' || itemData.status == '3'" type="primary" @click="hanldData(record)">上传</el-button>
                        <template #tip>
                          <div class="el-upload__tip">

                          </div>
                        </template>
                      </el-upload>
                    </template>
                    <template v-if="column.key === 'fileUrl'">
                      <el-button type="primary" :disabled="itemData.status == '5' || itemData.status == '3'" @click="fileDown(record)">下载</el-button>
                    </template>
                    <template v-if="column.key === 'action'">
                        <TableAction :actions="[
                          { icon: IconEnum.EDIT, label: t('action.edit'), onClick: preUpdate.bind(null, record,'edit'),disabled:itemData.status == '3' },
                          {
                            icon: IconEnum.DELETE,
                            danger: true,
                            label: t('action.delete'),
                            popConfirm: {
                              title: t('common.delMessage'),
                              placement: 'left',
                              confirm: handleDelete.bind(null, record,'部位'),
                            },
                            disabled:itemData.status == '3'
                          },
                        ]" />
                    </template>
                  </template>
                </BasicTable>

                <!-- 药品 -->
                <el-form ref="ypRef" v-if="isYaoPin" style="width: 100%;padding: 10px;color: rgba(0, 0, 0, 0.88);" :model="preData" size="small">
                  <el-form-item style="align-items:center" label="病人类型" required>
                    <el-radio-group v-model="ypform.patCometype">
                      <el-radio v-for="(item, oIndex) in getDictOptions(DICT_TYPE.EVT_PATIENT_TYPE,'string')"  :label="item.label" :value="item.value"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[0].name" :key="index" v-if="preData[0].type == '1'"
                    :required="preData[0].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[0].value">
                      <el-radio v-for="(item, oIndex) in preData[0].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[1].name" :key="index" v-if="preData[1].type == '1'"
                    :required="preData[1].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[1].value">
                      <el-radio v-for="(item, oIndex) in preData[1].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[2].name" :key="index" v-if="preData[2].type == '1'"
                    :required="preData[2].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[2].value">
                      <el-radio v-for="(item, oIndex) in preData[2].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <table class="table">
+                    <tbody>
                      <tr>
                        <td colspan="1">
                          <el-form-item label="患者姓名" required>
                            <!-- <a-input v-model:value="ypform.name" /> -->
                            <el-input style="min-width:80px;" v-model="ypform.patName" placeholder="输入患者姓名" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item label="性别" required>
                            <a-radio-group v-model:value="ypform.patSex">
                              <a-radio value="1">男</a-radio>
                              <a-radio value="2">女</a-radio>
                            </a-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item label="年龄" required >
                            <div>年 <a-input-number style="width:70%;" size="small" v-model:value="ypform.patNewYear" :min="0"/></div>
                            <div>月 <a-input-number style="width:70%;" size="small" v-model:value="ypform.patNewMonth" :min="1" :max="12" /></div>
                            <div>日 <a-input-number style="width:70%;" size="small" v-model:value="ypform.patNewDay" :min="0" :max="31" /></div>
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[3].type == '3'" :label="preData[3].name"
                            :required="preData[3].isRequire == '0' ? true : false">
                            <el-input v-model="preData[3].value" placeholder="患者民族" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[4].type == '6'" :label="preData[4].name"
                            :required="preData[4].isRequire == '0' ? true : false">
                            <!-- <el-input-number v-model="preData[4].value" :min="preData[4].min" :max="preData[4].max"  @change="changeNum" /> -->
                            <a-input-number size="small" v-model:value="preData[4].value" :min="preData[4].min" :max="preData[4].max" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[5].type == '3'" :label="preData[5].name"
                            :required="preData[5].isRequire == '0' ? true : false">
                            <el-input v-model="preData[5].value" placeholder="患者联系方式" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="2">
                          <el-form-item v-if="preData[6].type == '3'" :label="preData[6].name"
                            :required="preData[6].isRequire == '0' ? true : false">
                            <el-input v-model="preData[6].value" placeholder="原患疾病" />
                          </el-form-item>
                        </td>

                        <td colspan="2">
                          <el-form-item label="医院名称">
                            <el-input v-model="userData.orgName" />
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item label="病历/门诊号" required>
                            <el-input v-model="ypform.patHosnumber" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="7">

                          <el-form-item :label="preData[7].name" :key="index" v-if="preData[7].type == '1'"
                            :required="preData[7].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[7].value">
                              <el-radio v-for="(item, oIndex) in preData[7].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="7">

                          <el-form-item :label="preData[8].name" :key="index" v-if="preData[8].type == '1'"
                            :required="preData[8].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[8].value">
                              <el-radio v-for="(item, oIndex) in preData[8].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="7">
                          <el-form-item v-if="preData[9].type == '2'" :label="preData[9].name" :key="index"
                            :required="preData[9].isRequire == '0' ? true : false">
                            <el-checkbox-group v-model="preData[9].value">
                              <el-checkbox v-for="(item) in preData[9].options" :key="item.id" :label="item.name" :value="item.id" />
                            </el-checkbox-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[10].type == '9'" :label="preData[10].name" :key="index"
                            :required="preData[10].isRequire == '0' ? true : false">
                            <el-table :data="preData[10].tableData" style="width: 100%" max-height="300">
                              <el-table-column v-for="(item, oIndex) in preData[10].options" :key="oIndex" :prop="item.prop" :label="item.name">
                                <template #default="scope">
                                  <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                                </template>
                              </el-table-column>
                              <el-table-column fixed="right" label="操作" width="120">
                                <template #default="scope">
                                  <el-button
                                    link
                                    type="primary"
                                    size="small"
                                    @click.prevent="deleteRow(scope,scope.$index,preData[10])"
                                  >
                                    删除
                                  </el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                            <el-button class="mt-4" style="width: 100%" @click="onAddItem(preData[10],preData[10].options)">
                              添加
                            </el-button>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">

                          <el-form-item v-if="preData[11].type == '9'" :label="preData[11].name" :key="index"
                            :required="preData[11].isRequire == '0' ? true : false">
                            <el-table :data="preData[11].tableData" style="width: 100%" max-height="300">
                              <el-table-column v-for="(item, oIndex) in preData[11].options" :key="oIndex" :prop="item.prop" :label="item.name">
                                <template #default="scope">
                                  <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                                </template>
                              </el-table-column>
                              <el-table-column fixed="right" label="操作" width="120">
                                <template #default="scope">
                                  <el-button
                                    link
                                    type="primary"
                                    size="small"
                                    @click.prevent="deleteRow(scope,scope.$index,preData[11])"
                                  >
                                    删除
                                  </el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                            <el-button class="mt-4" style="width: 100%" @click="onAddItem(preData[11],preData[11].options)">
                              添加
                            </el-button>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="3">
                          <el-form-item v-if="preData[12].type == '3'" :label="preData[12].name"
                            :required="preData[12].isRequire == '0' ? true : false">
                            <el-input v-model="preData[12].value" placeholder="输入" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item v-if="preData[13].type == '7'" :label="preData[13].name"
                            :required="preData[13].isRequire == '0' ? true : false">
                            <el-date-picker v-model="preData[13].value" type="datetime" placeholder="不良反应/事件发生时间" locale="locale" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[14].type == '4'" :label="preData[14].name" :key="index"
                            :required="preData[14].isRequire == '0' ? true : false">
                            <el-input v-model="preData[14].value" :autosize="{ minRows: 2, maxRows: 4 }"
                              type="textarea" placeholder="输入不良事件反应/事件过程描述" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[15].name" :key="index" v-if="preData[15].type == '1'"
                            :required="preData[15].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[15].value">
                              <el-radio v-for="(item, oIndex) in preData[15].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[18].name" :key="index" v-if="preData[18].type == '1'"
                            :required="preData[18].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[18].value">
                              <el-radio v-for="(item, oIndex) in preData[18].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[19].name" :key="index" v-if="preData[19].type == '1'"
                            :required="preData[19].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[19].value">
                              <el-radio v-for="(item, oIndex) in preData[19].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[20].name" :key="index" v-if="preData[20].type == '1'"
                            :required="preData[20].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[20].value">
                              <el-radio v-for="(item, oIndex) in preData[20].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1" rowspan="2">
                          <el-form-item label="关联性评价"></el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item :label="preData[21].name" :key="index" v-if="preData[21].type == '1'"
                            :required="preData[21].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[21].value">
                              <el-radio v-for="(item, oIndex) in preData[21].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[22].type == '3'" :label="preData[22].name"
                            :required="preData[22].isRequire == '0' ? true : false">
                            <el-input v-model="preData[22].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="3">
                          <el-form-item :label="preData[23].name" :key="index" v-if="preData[23].type == '1'"
                            :required="preData[23].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[23].value">
                              <el-radio v-for="(item, oIndex) in preData[23].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[24].type == '3'" :label="preData[24].name"
                            :required="preData[24].isRequire == '0' ? true : false">
                            <el-input v-model="preData[24].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1" rowspan="2">
                          <el-form-item label="报告人信息"></el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[25].type == '3'" :label="preData[25].name" :key="index"
                            :required="preData[25].isRequire == '0' ? true : false">
                            <el-input v-model="preData[25].value" placeholder="报告人联系电话" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item :label="preData[26].name" :key="index" v-if="preData[26].type == '1'"
                            :required="preData[26].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[26].value">
                              <el-radio v-for="(item, oIndex) in preData[26].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          <el-form-item v-if="preData[27].type == '3'" :label="preData[27].name"
                            :required="preData[27].isRequire == '0' ? true : false">
                            <el-input v-model="preData[27].value" placeholder="报告联系人电子邮箱" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item v-if="preData[28].type == '3'" :label="preData[28].name"
                            :required="preData[28].isRequire == '0' ? true : false">
                            <el-input v-model="preData[28].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1">
                          <el-form-item label="报告单位信息 "></el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[29].type == '3'" :label="preData[29].name"
                            :required="preData[29].isRequire == '0' ? true : false">
                            <el-input v-model="preData[29].value" placeholder="单位名称" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[30].type == '3'" :label="preData[30].name"
                            :required="preData[30].isRequire == '0' ? true : false">
                            <el-input v-model="preData[30].value" placeholder="单位联系人" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[31].type == '3'" :label="preData[31].name"
                            :required="preData[31].isRequire == '0' ? true : false">
                            <el-input v-model="preData[31].value" placeholder="单位联系人电话" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[32].type == '7'" :label="preData[32].name"
                            :required="preData[32].isRequire == '0' ? true : false">
                            <el-date-picker v-model="preData[32].value" type="datetime" placeholder="选择时间" locale="locale" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[33].name" :key="index" v-if="preData[33].type == '1'"
                            :required="preData[33].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[33].value">
                              <el-radio v-for="(item, oIndex) in preData[33].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[34].type == '4'" :label="preData[34].name"
                            :required="preData[34].isRequire == '0' ? true : false">
                            <el-input v-model="preData[34].value" :autosize="{ minRows: 2, maxRows: 4 }"
                              type="textarea" placeholder="输入内容" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                            <el-form-item v-if="preData[35] && preData[35].type == '8'" :label="preData[35].name">
                              <el-upload
                                v-model:file-list="YPfileList"
                                class="upload-demo"
                                :http-request="uploadApi"
                                :on-change="YPhandleChange"
                                :on-preview='downFile'
                                :on-remove="YPremoveFile"
                                :before-upload="beforUpload"
                                :data="fileData"
                                multiple
                              >
                                <el-button type="primary">上传</el-button>
                                <template #tip>
                                  <div class="el-upload__tip">
                                    <!-- 只能上传jpg/png文件，且不超过500kb -->
                                  </div>
                                </template>
                              </el-upload>
                            </el-form-item>
                        </td>
                      </tr>
+                    </tbody>
                  </table>
                </el-form>
                <div style="text-align: right;"><a-button  type="primary" @click="saveForm2" :disabled="itemStatus">保存</a-button></div>

              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="2" header="患者信息" v-show="!isYaoPin" id="p2" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicForm :schemas="patient" ref="formRef1" :model="patientdata" @register="form1" :disabled="itemStatus" />
                <div style="text-align: right;"><a-button  type="primary" @click="saveForm1" :disabled="itemStatus">保存</a-button></div>
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="3" header="责任人信息" v-show="!isYaoPin" id="p3" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicTable @register="rTable1" :disabled="itemStatus">
                  <template #toolbar>
                    <a-button type="primary" @click="openSelect('人员选择')" :disabled="itemStatus"> 人员选择 </a-button>
                  </template>
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <TableAction :actions="[
                        {
                          icon: IconEnum.DELETE,
                          danger: true,
                          label: t('action.delete'),
                          popConfirm: {
                            title: t('common.delMessage'),
                            placement: 'left',
                            confirm: handleDelete.bind(null, record,'人员选择'),
                          },
                          disabled:itemData.status == '5' || itemData.status == '3',
                        },
                      ]" />
                    </template>
                  </template>
                </BasicTable>
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="4" header="上报人信息" v-show="!isYaoPin" id="p4" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicForm :schemas="schemas3" :model="formdata3" @register="form3" ref="formRef4" :disabled="itemStatus" />
                <div style="text-align: right;"><a-button  type="primary" @click="saveForm3" :disabled="itemStatus">保存</a-button></div>
              </a-collapse-panel>
            </a-collapse>
          </div>

        </div>
        <div class="right " id="rightRef" ref="rightRef">
          <div style="width: 100%;">
            <el-affix :offset="60">
              <el-anchor :container="rightRef" type="default" :offset="30" direction="horizontal" @click="handleClick">
                <el-anchor-link class="rightLabel" href="#r1" title="事件分析" />
                <el-anchor-link class="rightLabel" href="#r2" title="事件整改" />
              </el-anchor>
            </el-affix>
            <a-collapse v-model:activeKey="activeKeyRight">
              <a-collapse-panel class="custom-panel" key="1" header="事件分析" id="r1">
                <p class="pTitle">参会时间/地点/人员</p>
                <BasicForm :model="formdata3" ref="formRef5" @register="form4" :disabled="itemData.status == '5' " />
                <BasicTable title="参会人员" class="rTable2" @register="rTable2" @row-click="rowClick" >
                  <template #toolbar>
                    <a-button :disabled="itemData.status == '5' " type="primary" @click="openSelect('参会人员选择')"> 参会人员选择 </a-button>
                  </template>
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <TableAction :actions="[
                          {
                            icon: IconEnum.DELETE,
                            danger: true,
                            label: t('action.delete'),
                            popConfirm: {
                              title: t('common.delMessage'),
                              placement: 'left',
                              confirm: handleDelete.bind(null, record,'参会人员选择'),
                            },
                            disabled:itemData.status == '5',
                          },
                        ]" />
                    </template>
                  </template>
                  <!-- <TableAction :actions="createActions(record)" /> -->
                </BasicTable>

                <p class="pTitle">
                  <span>原因对策分析</span>
                  <a-button  @click="openyugutu" style="float:right">鱼骨图</a-button>
                </p>
                <a-row style="height: 100%;min-height: 300px;" :gutter="16">
                  <a-col style="height: 100%;" :span="12">
                    <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="原因知识库" :bordered="false">
                      <template #extra>
                        <a-button :disabled="itemData.status == '5' " type="primary" @click="handOpen('原因选择')">原因选择</a-button>
                      </template>
                      <el-input v-model="filterTextLeft" style="width: 100%" placeholder="搜索" />
                      <el-tree class="treeStyle" ref="treeLeftRef" :data="treeLeft" node-key="id" :expand-on-click-node="true"
                       :filter-node-method="filterNode1" :props="prop1" default-expand-all highlight-current />
                    </a-card>
                  </a-col>
                  <a-col style="height: 100%;" :span="12">
                    <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="对策知识库" :bordered="false">
                      <template #extra>
                        <a-button :disabled="itemData.status == '5' " type="primary" @click="handOpen('措施选择')">措施选择</a-button>
                      </template>
                      <el-input v-model="filterTextRight" style="width: 100%" placeholder="搜索" />
                      <el-tree class="treeStyle" ref="treeRightRef" :data="treeRight" node-key="id"
                        :expand-on-click-node="false" :filter-node-method="filterNode2" :props="prop2" default-expand-all />
                    </a-card>
                  </a-col>
                </a-row>

                <p class="pTitle">附件上传</p>
                  <el-upload
                    :disabled="itemData.status == '5' "
                    v-model:file-list="fileList"
                    class="upload-demo"
                    :data="fileData"
                    :before-upload="beforUpload"
                    :http-request="uploadApi"
                    :on-change="handleChange"
                    :on-preview='downFile'
                    :on-remove="removeFile"
                    multiple
                  >
                  <!-- :on-change="handleChange"  :on-success="handleChange"  -->

                    <el-button type="primary">上传</el-button>
                    <template #tip>
                      <div class="el-upload__tip">

                      </div>
                    </template>
                  </el-upload>

                <p class="pTitle">会议总结</p>
                <a-textarea :disabled="itemData.status == '5' " v-model:value="formdata3.sumnotes" @blur="onBlur" placeholder="请输入内容" :rows="4" />


              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="2" header="事件整改" id="r2">
                <BasicTable title="对策措施整改" :columns="tableData5" :loading="loading" :pagination="{ pageSize: 20 }"
                  @register="rTable5">
                  <template #toolbar>
                    <a-button type="primary" :disabled="itemData.status == '5' " @click="openModel3('add')"> 新增整改 </a-button>
                  </template>

                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <TableAction :actions="[
                          {
                            icon: IconEnum.DELETE,
                            danger: true,
                            label: t('action.delete'),
                            popConfirm: {
                              title: t('common.delMessage'),
                              placement: 'left',
                              confirm: handleDelete.bind(null, record,'措施整改'),
                            },
                            disabled:itemData.status == '5',
                          },
                          { icon: IconEnum.EDIT, label: t('确认整改'),ifShow: !record.abaStatus, auth: 'evt:info:update', onClick: handleUpdate.bind(null, record,'0'),
                            // ifShow: (_column) => {
                            //   return true; // 根据业务控制是否显示
                            // },
                            disabled:itemData.status == '5',
                          },
                          { icon: IconEnum.EDIT, label: t('查看'),ifShow: record.evaStatus == 1 && record.abaStatus == 1, auth: 'evt:info:update', onClick: handleUpdate.bind(null, record,'2'),},
                          { icon: IconEnum.EDIT, label: t('待评价'),disabled:itemData.status == '5',
                            ifShow: record.abaStatus === '1' && record.evaStatus != 1 ,
                            auth: 'evt:info:update', onClick: handleUpdate.bind(null, record,'1')
                          },
                        ]" />
                    </template>
                  </template>
                </BasicTable>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </div>

      </div>
      <template #insertFooter>
        <div style="float: left;margin-left: 20px;">
          <a-button type="primary" @click="onClickfd('left')" > 左 </a-button>
          <a-button type="primary" @click="onClickfd('right')" > 右 </a-button>
          <a-button type="primary" @click="onClickfd('center')" > 居中 </a-button>
        </div>
      </template>
      <!-- <BasicForm v-else @register="registerForm" /> -->
    </BasicModal>
    <a-modal :maskClosable="false" v-model:open="openModel" @ok="handleOk" style="width: 60%;" :bodyStyle="{ padding: '40px 10px 10px' }" :zIndex="1001">
      <BasicTable @register="modelTable" :pagination="{ pageSize: 10 }" :disabled="false" >
      </BasicTable>
    </a-modal>
    <a-modal :maskClosable="false" v-model:open="modelOne" @ok="handleOkTree" style="width: 60%;" :zIndex="1001">

      <a-row style="height: 100%;min-height: 300px;margin-top: 40px;" :gutter="16">
        <a-col v-if="treeModelTitle=='原因选择'" style="height: 100%;" :span="12">
          <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="原因知识库" :bordered="false">
            <el-input v-model="filterText1" style="width: 100%" placeholder="搜索" />
            <el-tree class="treeStyle" ref="treeRef1" :data="treeData" node-key="id" :check-strictly="strictlyFlag"
              :expand-on-click-node="true" show-checkbox @check="onCheck" :filter-node-method="filterNode1" :props="prop1" />
          </a-card>
        </a-col>
        <a-col v-else style="height: 100%;" :span="10">
          <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="对策知识库" :bordered="false">
            <!-- <template #extra>{{causeData.causeName}}</template> -->
            <el-input v-model="filterText2" style="width: 100%" placeholder="搜索" />
            <el-tree class="treeStyle" ref="treeRef2" :data="treeData2" show-checkbox node-key="id"
              :expand-on-click-node="true" :filter-node-method="filterNode2" :props="prop2" :check-strictly="strictlyFlag"
               @check="onCheck" />
          </a-card>
        </a-col>
        <a-col style="height: 100%;" :span="12">
          <a-card :bodyStyle="{ padding: '10px 24px' }" style="height: 100%;" :title="treeModelTitle=='原因选择' ? '原因知识库' : '对策知识库'">
            <template #extra>
              <a-button type="primary" @click="modelTwo = true" :disabled="!treeData3.length">临时新增</a-button>
            </template>
            <el-tree v-if="modelOne" class="treeStyle" ref="treeRef3" :data="treeData3" node-key="id" default-expand-all :props="treeModelTitle=='原因选择' ? prop1 : prop2"
              :expand-on-click-node="false" >
            </el-tree>
          </a-card>
        </a-col>
      </a-row>

    </a-modal>

    <a-modal :maskClosable="false" v-model:open="modelTwo" @ok="handOk" style="width: 50%;" :zIndex="1002">
      <a-form ref="formRef22" :model="modelForm" style="margin: 20px;" :label-col="{ span: 4 }" :wrapper-col="{ span: 14 }">
        <a-form-item label="名称：" :name="treeModelTitle=='原因选择' ? 'causeName' : 'measuresName'" required>
          <a-input v-if="treeModelTitle=='原因选择'" v-model:value="modelForm.causeName" />
          <a-input v-else v-model:value="modelForm.measuresName" />
        </a-form-item>
        <a-form-item label="所属父级名称：" name="parentId"  required>
          <!-- <div style="display: flex;">
            <a-input v-model:value="modelForm.parentName" disabled />
            <a-button type="primary" @click="modelThree = true">选择父级名称</a-button>
          </div> -->
          <el-tree v-if="modelTwo" class="treeStyle" ref="treeRef22" :data="treeData3" node-key="id" highlight-current
          default-expand-all :props="treeModelTitle=='原因选择' ? prop1 : prop2" @node-click="handleClickp" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal :maskClosable="false" :ok-button-props="{ disabled: dontEdit }"
      v-model:open="modelThree" @ok="handOkThree" style="width: 50%;" :bodyStyle="{ padding: '40px 10px 10px' }" :zIndex="1002">
      <BasicForm @register="modelThreeForm" :disabled="dontEdit" />
    </a-modal>

    <a-modal :maskClosable="false" v-model:open="addModel" style="width: 80%;" @cancel="addCancel" :footer="null" :bodyStyle="{ padding: '40px 10px 10px' }" :zIndex="1002">
      <BasicTable @register="addTable" id="addtable">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key === 'evtName'">
            <Tag color="green" v-for="item in record.evtName" @click="handEvent(item)" style="cursor: pointer;margin:1px 2px;">
              {{ item.evtName }}
            </Tag>
          </template>
        </template>
      </BasicTable>
    </a-modal>

    <!-- 压疮 -->
    <a-modal :maskClosable="false" v-model:open="modelFour" style="width: 70%;" :zIndex="1002" :footer="null" :bodyStyle="{ padding: '40px 10px 10px' }">
      <BasicForm  :model='ycForm' @register="registerYc" @submit='ycSubmit' />
    </a-modal>

    <a-modal
      v-model:open="auditModal"
      style="width:70%"
      title="事件上报审核"
      centered
      @ok="auditModal = false"
      :zIndex="1001"
      :maskClosable="false"
    >
      <BasicTable :columns="auditData" @register="auditTable"></BasicTable>
    </a-modal>
    <!-- 鱼骨图 -->
    <a-modal :maskClosable="false" v-model:open="yuguModel" style="width: 80%;" :zIndex="1003" :footer="null" :bodyStyle="{ padding: '40px 10px 10px' }">
      <div id="fishboneWrap" style="height: 100%;">
        <div class="contentWrap">
          <FishboneDialog
            style="width:100%;"
            class="fullscreen"
            ref="sun"
            :repId="eventnumber"
            :name="fbData.head"
            :rjlhfListALL="fbData"
          ></FishboneDialog>
        </div>
        <div class="changeSize">
          <a-button
            class="size-but"
            type="text"
            size="mini"
            @click="canvasSize('add', 'sun')"
          >
          <template #icon>
            <PlusCircleOutlined />
          </template>
            放大</a-button>
          <a-button
            class="size-but"
            type="text"
            size="mini"
            @click="canvasSize('minus', 'sun')"
          >
          <MinusCircleOutlined />
            缩小 </a-button>
          <a-button
            class="size-but"
            type="text"
            size="mini"
            @click="canvasSize('restore', 'sun')"
          ><ExpandOutlined /> 还原</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
#addtable{
  ::v-deep(.ant-table td) {
    white-space: unset;
  }
}
.table{
  min-width: 50%;
  max-width: 100%;
  vertical-align: middle;
  margin-top:20px;
  tr{
    border: 1px solid #000;
    vertical-align: middle;
    td{
      .el-form-item{
        margin: 6px 0;
        padding:0 2px;
        align-items: center;
      }
      ::v-deep(:where(.css-dev-only-do-not-override-12ru88o).ant-form-item) {
        margin: 6px 0;
      }
      border: 1px solid #000;
      vertical-align: middle
    }
  }
}
.pTitle{
  font-size: 16px;
  font-weight: bold;
  padding: 10px 0 0 0;
}
::v-deep(.tableRowStyle){
  td{
    background-color: rgba(255, 157, 101, 0.438);
  }
}
.rTable2 ::v-deep(.ant-table-tbody .ant-table-row:hover > td){
  background-color: rgba(255, 157, 101, 0.438)!important;
}
.custom-panel ::v-deep(.ant-collapse-header) {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
// .el-anchor ::v-deep(.el-anchor__link.is-active) {
//   background-color: #409eff; /* 蓝色背景 */
//   color:red ; /* 白色文字 */
// }
.treeStyle {
  max-width: 600px;
  height: 400px;
  overflow: scroll;
  overflow-x: visible;
}
.lrFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.container1 {
  display: flex;
  justify-content: space-between;
  height: 100%;
  width: 100%;

  .left {
    display: flex;
    width: 50%;
    height: 100%;
    overflow-x: scroll;
    .leftLabel {
      // width: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;

      :deep(a) {
        color: #000;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }

  .right {
    display: flex;
    width: 50%;
    height: 100%;
    overflow-x: scroll;
    padding-left: 5px;

    .rightLabel {
      // width: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;

      :deep(a) {
        color: #000;
        font-weight: bold;
        font-size: 18px;
      }
    }


  }
}


.scale-down-hor-left {
      -webkit-animation: scale-down-hor-left 0.4s both;
              animation: scale-down-hor-left 0.4s both;
}
@-webkit-keyframes scale-down-hor-left {
  0% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
}
@keyframes scale-down-hor-left {
  0% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
}
.scale-down-hor-right {
  -webkit-animation: scale-down-hor-right 0.4s both;
          animation: scale-down-hor-right 0.4s both;
}

@-webkit-keyframes scale-down-hor-right {
  0% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 100% 100%;
            transform-origin: 100% 100%;
  }
}
@keyframes scale-down-hor-right {
  0% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
            transform-origin: 0% 0%;
  }
  100% {
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
    -webkit-transform-origin: 100% 100%;
            transform-origin: 100% 100%;
  }
}

#fishboneWrap{
  margin: 0 0 5px;
  height: 100%;

  .contentWrap{
    display: flex;
    width: 100%;
    height: calc(100vh - 315px);
    padding: 0 15px;
  }
  .leftBoxfish {
    width: 20%;
    border: 1px solid #5d686e;
    border-radius: 5px;
    position: relative;
    .fishSort {
      height: 20px;
      width: 20px;
      text-align: center;
      line-height: 20px;
      border: 1px solid #ccc;
      border-radius: 10px;
      margin-right: 5px;
    }
    .fishText {
      height: 30px;
      width: 90%;
      line-height: 30px;
      border: 1px solid #ccc;
      padding: 0 5px 0 20px;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
    }
    .fullscreen {
      width: 99.8%;
    }
  }
  .changeSize {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
    padding: 0 15px;
  }
  .sizeicon {
    font-weight: 600;
  }
  .size-but {
    color: #0960bd;
    font-size: 15px;
    font-weight: 600;
    padding: 4px 2px;
    span{
      margin:0 0 0 5px !important;
    }
  }
}
</style>