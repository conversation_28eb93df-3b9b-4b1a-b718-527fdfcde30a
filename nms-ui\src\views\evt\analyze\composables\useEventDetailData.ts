/**
 * 事件详情数据管理Composable
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-27
 */

import { ref, reactive, readonly, computed } from 'vue'
import { message } from 'ant-design-vue'
import { defHttp } from '@/utils/http/axios'
import { getEventInfo } from '@/api/evt/analyze'
import type {
  EventDetailData,
  LoadingState,
  ErrorState,
  DynamicField,
  AttachmentFile
} from '../types/eventDetail'
import {
  validateEventId,
  validateApiResponse,
  safeGet,
  formatFileInfo,
  formatDynamicField,
  formatPatientInfo,
  handleApiError,
  logDataLoadingStats,
  createRetryFunction,
  checkDataIntegrity
} from '../utils/dataValidation'

export function useEventDetailData(eventId: string) {
  // 响应式数据
  const eventDetailData = ref<EventDetailData | null>(null)
  
  // 加载状态
  const loading = reactive<LoadingState>({
    basic: false,
    patient: false,
    dynamic: false,
    attachments: false,
    audit: false,
    all: false
  })
  
  // 错误状态
  const errors = reactive<ErrorState>({
    basic: null,
    patient: null,
    dynamic: null,
    attachments: null,
    audit: null
  })
  
  // 计算属性
  const hasPatientInfo = computed(() => {
    return eventDetailData.value?.patientInfo && 
           eventDetailData.value.patientInfo.patName
  })
  
  const hasDynamicFields = computed(() => {
    return eventDetailData.value?.dynamicFields && 
           eventDetailData.value.dynamicFields.length > 0
  })
  
  const hasAttachments = computed(() => {
    return eventDetailData.value?.attachments && 
           eventDetailData.value.attachments.length > 0
  })
  
  const isAnyLoading = computed(() => {
    return Object.values(loading).some(state => state)
  })
  
  // 清除错误
  const clearError = (type: keyof ErrorState) => {
    errors[type] = null
  }
  
  // 设置错误
  const setError = (type: keyof ErrorState, error: string) => {
    errors[type] = error
  }
  
  // 加载基础信息
  const loadBasicInfo = async () => {
    loading.basic = true
    clearError('basic')

    try {
      console.log('🔍 [EventDetailData] 开始加载基础信息:', eventId)

      // 数据验证：确保eventId有效
      if (!validateEventId(eventId)) {
        throw new Error('事件ID无效，无法加载基础信息')
      }

      // 检查是否是任务ID，如果是则先获取真实的事件ID
      let realEventId = eventId
      if (eventId.startsWith('REAL_TASK_')) {
        console.log('🔍 [EventDetailData] 检测到任务ID，获取真实事件ID:', eventId)
        try {
          const { getAnalyzeTaskDetail } = await import('@/api/evt/analyze')
          const taskResponse = await getAnalyzeTaskDetail(eventId)
          if (taskResponse && taskResponse.eventId) {
            realEventId = taskResponse.eventId
            console.log('🔍 [EventDetailData] 获取到真实事件ID:', realEventId)
          } else {
            throw new Error('无法从任务中获取事件ID')
          }
        } catch (error) {
          console.error('❌ [EventDetailData] 获取真实事件ID失败:', error)
          throw new Error('无法获取真实事件ID')
        }
      }

      const response = await getEventInfo(realEventId)

      console.log('🔍 [EventDetailData] 基础信息API响应:', response)

      // 验证API响应
      if (!validateApiResponse(response, ['id', 'evtname'])) {
        throw new Error('API返回数据格式异常')
      }

      // 检查数据完整性
      const integrityCheck = checkDataIntegrity(response, ['id', 'evtname'])
      if (integrityCheck.warnings.length > 0) {
        console.warn('⚠️ [EventDetailData] 数据完整性警告:', integrityCheck.warnings)
      }

      // 构建完整的事件详情数据
      eventDetailData.value = {
        basicInfo: {
          id: safeGet(response, 'id', eventId),
          evtname: safeGet(response, 'evtname') || safeGet(response, 'eventName', '未知事件'),
          evtType: safeGet(response, 'evtType') || safeGet(response, 'eventType'),
          evtClass: safeGet(response, 'evtClass') || safeGet(response, 'eventClass'),
          evtLevel: safeGet(response, 'evtLevel') || safeGet(response, 'eventLevel'),
          evTime: safeGet(response, 'evTime') || safeGet(response, 'eventTime') || safeGet(response, 'occurTime'),
          evDeptId: safeGet(response, 'evDeptId') || safeGet(response, 'deptId'),
          evDeptName: safeGet(response, 'evDeptName') || safeGet(response, 'deptName'),
          status: safeGet(response, 'status'),
          experience: safeGet(response, 'experience') || safeGet(response, 'description'),
          evtCritical: safeGet(response, 'evtCritical') || safeGet(response, 'critical'),
          sumnotes: safeGet(response, 'sumnotes') || safeGet(response, 'summary'),
          createTime: safeGet(response, 'createTime'),
          updateTime: safeGet(response, 'updateTime')
        },
        auditInfo: {
          audDuNature: safeGet(response, 'audDuNature'),
          audDuLevel: safeGet(response, 'audDuLevel'),
          auditPersonRespVO: safeGet(response, 'auditPersonRespVO'),
          infoAuditId: safeGet(response, 'infoAuditId')
        },
        patientInfo: formatPatientInfo(safeGet(response, 'infoPatientRespVO') || safeGet(response, 'patientInfo')),
        dynamicFields: [],
        attachments: response.fileList ? response.fileList.map(formatFileInfo).filter(Boolean) : [],
        reporterInfo: {
          repUserid: safeGet(response, 'repUserid') || safeGet(response, 'reporterId'),
          reporterName: safeGet(response, 'reporterName') || safeGet(response, 'repUsername') || safeGet(response, 'reporter'),
          reportDeptName: safeGet(response, 'reportDeptName') || safeGet(response, 'repDeptName') || safeGet(response, 'reportDept'),
          repAge: safeGet(response, 'repAge', 0) || safeGet(response, 'reporterAge', 0),
          repWorkyear: safeGet(response, 'repWorkyear', 0) || safeGet(response, 'workYear', 0),
          repTitle: safeGet(response, 'repTitle') || safeGet(response, 'title'),
          repPhone: safeGet(response, 'repPhone') || safeGet(response, 'phone'),
          repTime: safeGet(response, 'repTime') || safeGet(response, 'reportTime'),
          repSex: safeGet(response, 'repSex') || safeGet(response, 'sex'),
          isAnonymity: safeGet(response, 'isAnonymity') || safeGet(response, 'anonymous')
        }
      }

      console.log('✅ [EventDetailData] 基础信息加载成功:', {
        eventId: eventDetailData.value.basicInfo.id,
        eventName: eventDetailData.value.basicInfo.evtname,
        hasPatientInfo: !!eventDetailData.value.patientInfo,
        attachmentsCount: eventDetailData.value.attachments.length,
        hasAuditInfo: !!(eventDetailData.value.auditInfo.audDuNature || eventDetailData.value.auditInfo.audDuLevel),
        reporterName: eventDetailData.value.reporterInfo.reporterName
      })

    } catch (error) {
      const errorMsg = handleApiError(error, '基础信息加载')
      setError('basic', errorMsg)
      message.error(`加载基础信息失败: ${errorMsg}`)
    } finally {
      loading.basic = false
    }
  }
  
  // 加载动态字段
  const loadDynamicFields = async () => {
    loading.dynamic = true
    clearError('dynamic')

    try {
      console.log('🔍 [EventDetailData] 开始加载动态字段:', eventId)

      // 数据验证：确保eventId有效
      if (!eventId || eventId.trim() === '') {
        throw new Error('事件ID无效，无法加载动态字段')
      }

      const response = await defHttp.get({
        url: '/evt/info-content/findSysQuestions',
        params: { infoId: eventId }
      })

      console.log('🔍 [EventDetailData] 动态字段API响应:', response)

      if (response && (response.data || response)) {
        const data = response.data || response

        // 数据验证：确保返回的是数组
        if (!Array.isArray(data)) {
          console.warn('⚠️ [EventDetailData] 动态字段API返回数据格式异常，期望数组但得到:', typeof data)
          if (eventDetailData.value) {
            eventDetailData.value.dynamicFields = []
          }
          return
        }

        const dynamicFields: DynamicField[] = data
          .map((field: any, index: number) => formatDynamicField(field, index))
          .filter(Boolean)
          .sort((a: DynamicField, b: DynamicField) => a.order - b.order)

        if (eventDetailData.value) {
          eventDetailData.value.dynamicFields = dynamicFields
        }

        console.log('✅ [EventDetailData] 动态字段加载成功:', {
          totalFields: dynamicFields.length,
          fieldsWithValue: dynamicFields.filter(f => f.hasValue).length,
          emptyFields: dynamicFields.filter(f => f.isEmpty).length,
          fields: dynamicFields.map(f => ({ key: f.key, label: f.label, hasValue: f.hasValue }))
        })
      } else {
        console.log('ℹ️ [EventDetailData] 动态字段API返回空数据，此事件可能没有专项信息')
        if (eventDetailData.value) {
          eventDetailData.value.dynamicFields = []
        }
      }

    } catch (error) {
      const errorMsg = handleApiError(error, '动态字段加载')
      setError('dynamic', errorMsg)

      // 确保在错误情况下也设置空数组
      if (eventDetailData.value) {
        eventDetailData.value.dynamicFields = []
      }
    } finally {
      loading.dynamic = false
    }
  }
  
  // 重试机制
  const retryWithDelay = createRetryFunction

  // 并行加载所有数据
  const loadAllData = async () => {
    loading.all = true

    try {
      console.log('🔍 [EventDetailData] 开始并行加载所有数据:', eventId)

      // 并行执行所有数据加载，使用Promise.allSettled确保即使部分失败也能继续
      const results = await Promise.allSettled([
        retryWithDelay(loadBasicInfo, 2, 1000, '基础信息加载')(),
        retryWithDelay(loadDynamicFields, 2, 1000, '动态字段加载')()
      ])

      // 分析加载结果
      const taskNames = ['基础信息', '动态字段']
      let successCount = 0
      let failureCount = 0
      const details: any = {}

      results.forEach((result, index) => {
        const taskName = taskNames[index]
        if (result.status === 'fulfilled') {
          successCount++
          details[taskName] = '成功'
        } else {
          failureCount++
          details[taskName] = `失败: ${result.reason?.message || '未知错误'}`
        }
      })

      // 使用统计日志工具
      logDataLoadingStats('事件详情数据', {
        total: results.length,
        success: successCount,
        failed: failureCount,
        details
      })

      // 根据结果显示用户提示
      if (failureCount === results.length) {
        message.error('所有数据加载失败，请检查网络连接或联系管理员')
      } else if (failureCount > 0) {
        message.warning(`部分数据加载失败（${failureCount}/${results.length}），页面可能显示不完整`)
      }

    } catch (error) {
      console.error('❌ [EventDetailData] 数据加载过程中出现意外错误:', error)
      message.error('数据加载过程中出现意外错误')
    } finally {
      loading.all = false
    }
  }

  // 刷新数据
  const refreshData = async () => {
    console.log('🔄 [EventDetailData] 开始刷新数据')
    await loadAllData()
  }

  // 重试特定数据类型
  const retryDataType = async (dataType: keyof ErrorState) => {
    console.log(`🔄 [EventDetailData] 重试加载${dataType}数据`)
    clearError(dataType)

    const retryFunctions = {
      basic: () => retryWithDelay(loadBasicInfo, 2, 1000, '基础信息重试')(),
      dynamic: () => retryWithDelay(loadDynamicFields, 2, 1000, '动态字段重试')(),
      patient: () => Promise.resolve(), // 患者信息包含在基础信息中
      attachments: () => Promise.resolve(), // 附件信息包含在基础信息中
      audit: () => Promise.resolve() // 审核信息包含在基础信息中
    }

    const retryFunction = retryFunctions[dataType]
    if (retryFunction) {
      await retryFunction()
    } else {
      console.warn(`⚠️ [EventDetailData] 未知的数据类型: ${dataType}`)
    }
  }
  
  return {
    // 数据
    eventDetailData: readonly(eventDetailData),
    loading: readonly(loading),
    errors: readonly(errors),

    // 计算属性
    hasPatientInfo,
    hasDynamicFields,
    hasAttachments,
    isAnyLoading,

    // 方法
    loadAllData,
    loadBasicInfo,
    loadDynamicFields,
    refreshData,
    retryDataType,
    clearError,
    setError
  }
}
