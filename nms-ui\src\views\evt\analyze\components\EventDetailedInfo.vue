<!--
  事件详细信息容器组件
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-01-27
-->

<template>
  <div class="event-detailed-info">
    <!-- 信息导航器 -->
    <div class="info-navigator" v-if="showNavigator">
      <a-affix :offset-top="80">
        <div class="navigator-container">
          <a-anchor 
            :affix="false" 
            :bounds="5"
            :target-offset="100"
            class="info-anchor"
          >
            <a-anchor-link 
              v-for="section in visibleSections"
              :key="section.key"
              :href="`#${section.key}`" 
              :title="section.title"
              :class="{ 'has-data': section.hasData }"
            />
          </a-anchor>
        </div>
      </a-affix>
    </div>
    
    <!-- 详细信息区块 -->
    <div class="info-sections" :class="{ 'with-navigator': showNavigator }">
      <!-- 患者信息区块 -->
      <PatientInfoSection
        v-if="shouldShowSection('patient')"
        :patient-info="eventDetailData?.patientInfo"
        :loading="loading.patient"
        :collapsible="true"
        :default-expanded="getSectionExpanded('patient')"
        :show-analysis-tips="true"
      />
      
      <!-- 动态字段区块 -->
      <DynamicFieldsSection
        v-if="shouldShowSection('dynamic')"
        :dynamic-fields="eventDetailData?.dynamicFields || []"
        :loading="loading.dynamic"
        :collapsible="true"
        :default-expanded="getSectionExpanded('dynamic')"
        :show-summary="true"
        :show-suggestions="true"
      />
      
      <!-- 附件区块 -->
      <AttachmentsSection
        v-if="shouldShowSection('attachments')"
        :attachments="eventDetailData?.attachments || []"
        :loading="loading.attachments"
        :collapsible="true"
        :default-expanded="getSectionExpanded('attachments')"
        :show-summary="true"
        :show-thumbnails="true"
        :show-analysis-tips="true"
        @preview="handleFilePreview"
        @download="handleFileDownload"
        @reference="handleFileReference"
      />
      
      <!-- 审核信息区块 -->
      <AuditInfoSection
        v-if="shouldShowSection('audit')"
        :audit-info="eventDetailData?.auditInfo || {}"
        :loading="loading.audit"
        :collapsible="true"
        :default-expanded="getSectionExpanded('audit')"
        :show-timeline="true"
        :show-suggestions="true"
      />
      
      <!-- 上报信息区块 -->
      <ReporterInfoSection
        v-if="shouldShowSection('reporter')"
        :reporter-info="eventDetailData?.reporterInfo"
        :loading="loading.basic"
        :collapsible="true"
        :default-expanded="getSectionExpanded('reporter')"
        :show-contact-info="true"
      />
    </div>
    
    <!-- 加载状态覆盖 -->
    <div v-if="isInitialLoading" class="loading-overlay">
      <a-spin size="large" tip="正在加载详细信息...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>
    
    <!-- 错误状态显示 -->
    <div v-if="hasErrors && !isInitialLoading" class="error-container">
      <a-alert
        message="部分信息加载失败"
        description="某些详细信息可能无法显示，请尝试刷新页面"
        type="warning"
        show-icon
        closable
        class="error-alert"
      >
        <template #action>
          <a-button size="small" @click="handleRetry">
            重试
          </a-button>
        </template>
      </a-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import PatientInfoSection from './sections/PatientInfoSection.vue'
import DynamicFieldsSection from './sections/DynamicFieldsSection.vue'
import AttachmentsSection from './sections/AttachmentsSection.vue'
import AuditInfoSection from './sections/AuditInfoSection.vue'
import ReporterInfoSection from './sections/ReporterInfoSection.vue'
import type { 
  EventDetailData, 
  LoadingState, 
  ErrorState,
  AttachmentFile 
} from '../types/eventDetail'

// 组件属性
interface Props {
  eventDetailData: EventDetailData | null
  loading: LoadingState
  errors: ErrorState
  showNavigator?: boolean
  sectionConfig?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  showNavigator: true,
  sectionConfig: () => ({})
})

// 组件事件
const emit = defineEmits<{
  retry: []
  filePreview: [file: AttachmentFile]
  fileDownload: [file: AttachmentFile]
  fileReference: [file: AttachmentFile]
}>()

// 响应式数据
const sectionExpanded = ref<Record<string, boolean>>({
  patient: true,
  dynamic: true,
  attachments: true,
  audit: true,
  reporter: false
})

// 信息区块配置
const sectionConfigs = [
  {
    key: 'patient',
    title: '患者信息',
    priority: 1,
    hasData: computed(() => !!props.eventDetailData?.patientInfo?.patName)
  },
  {
    key: 'dynamic',
    title: '专项信息',
    priority: 2,
    hasData: computed(() => (props.eventDetailData?.dynamicFields?.length || 0) > 0)
  },
  {
    key: 'attachments',
    title: '相关附件',
    priority: 3,
    hasData: computed(() => (props.eventDetailData?.attachments?.length || 0) > 0)
  },
  {
    key: 'audit',
    title: '审核信息',
    priority: 4,
    hasData: computed(() => !!(
      props.eventDetailData?.auditInfo?.audDuNature ||
      props.eventDetailData?.auditInfo?.audDuLevel ||
      props.eventDetailData?.auditInfo?.auditPersonRespVO
    ))
  },
  {
    key: 'reporter',
    title: '上报信息',
    priority: 5,
    hasData: computed(() => !!props.eventDetailData?.reporterInfo?.reporterName)
  }
]

// 计算属性
const isInitialLoading = computed(() => {
  return props.loading.all || props.loading.basic
})

const hasErrors = computed(() => {
  return Object.values(props.errors).some(error => error !== null)
})

const visibleSections = computed(() => {
  return sectionConfigs
    .filter(section => shouldShowSection(section.key))
    .sort((a, b) => a.priority - b.priority)
    .map(section => ({
      key: section.key,
      title: section.title,
      hasData: section.hasData.value
    }))
})

// 方法
const shouldShowSection = (sectionKey: string) => {
  const config = sectionConfigs.find(s => s.key === sectionKey)
  if (!config) return false
  
  // 如果有配置，使用配置决定是否显示
  if (props.sectionConfig[sectionKey] !== undefined) {
    return props.sectionConfig[sectionKey]
  }
  
  // 默认显示有数据的区块，或者总是显示某些重要区块
  return config.hasData.value || ['patient', 'dynamic'].includes(sectionKey)
}

const getSectionExpanded = (sectionKey: string) => {
  return sectionExpanded.value[sectionKey] ?? true
}

const handleRetry = () => {
  emit('retry')
}

const handleFilePreview = (file: AttachmentFile) => {
  emit('filePreview', file)
}

const handleFileDownload = (file: AttachmentFile) => {
  emit('fileDownload', file)
}

const handleFileReference = (file: AttachmentFile) => {
  emit('fileReference', file)
}

// 监听数据变化，自动调整区块展开状态
watch(
  () => props.eventDetailData,
  (newData) => {
    if (newData) {
      // 如果有患者信息，默认展开患者信息区块
      if (newData.patientInfo?.patName) {
        sectionExpanded.value.patient = true
      }
      
      // 如果有动态字段，默认展开动态字段区块
      if (newData.dynamicFields && newData.dynamicFields.length > 0) {
        sectionExpanded.value.dynamic = true
      }
      
      // 如果有附件，默认展开附件区块
      if (newData.attachments && newData.attachments.length > 0) {
        sectionExpanded.value.attachments = true
      }
      
      // 如果有审核信息，默认展开审核区块
      if (newData.auditInfo?.audDuNature || newData.auditInfo?.audDuLevel) {
        sectionExpanded.value.audit = true
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.event-detailed-info {
  position: relative;
  
  .info-navigator {
    position: fixed;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    
    .navigator-container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 12px 0;
      min-width: 120px;
      
      .info-anchor {
        :deep(.ant-anchor-link) {
          padding: 4px 16px;
          
          .ant-anchor-link-title {
            font-size: 12px;
            color: #8c8c8c;
            transition: all 0.3s ease;
          }
          
          &.has-data .ant-anchor-link-title {
            color: #1890ff;
            font-weight: 500;
          }
          
          &.ant-anchor-link-active .ant-anchor-link-title {
            color: #1890ff;
            font-weight: 500;
          }
        }
        
        :deep(.ant-anchor-ink) {
          width: 2px;
          background: #1890ff;
        }
      }
    }
  }
  
  .info-sections {
    &.with-navigator {
      margin-right: 160px; // 为导航器留出空间
    }
    
    > * {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    
    .loading-placeholder {
      width: 100%;
      height: 200px;
    }
  }
  
  .error-container {
    margin-bottom: 16px;
    
    .error-alert {
      border-radius: 6px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .event-detailed-info {
    .info-navigator {
      display: none; // 小屏幕隐藏导航器
    }
    
    .info-sections.with-navigator {
      margin-right: 0;
    }
  }
}

@media (max-width: 768px) {
  .event-detailed-info {
    .info-sections {
      > * {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
