<script lang="ts" setup>
import { ref, unref, watch,onMounted,nextTick } from 'vue'
import { patient,schemas3,evtData,addTableData,addSeach,tableData1,tableData2,tableData3,tableData5,yachuangSchema,preTable} from './info.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm, FormSchema} from '@/components/Form'
import { Tag } from 'ant-design-vue';
import { BasicModal, useModalInner } from '@/components/Modal'
import { 
  getInfoPressureSorePage,
  findSysQuestions,getEvtcontent, getInfo,updateInfoDiscussant,getInfoAssignmentPage, getInfoPatient,getEvtnameData,findTreeInfoCause,getInfoDiscussantPage,
   getInfoResponsiblePersonsPage,listSimpleDept,findTreeInfoMeasures
} from '@/api/evt/info'
import { BasicTable, useRender, TableAction,BasicColumn,useTable } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict';
import { BasicUpload } from '@/components/Upload'
import { uploadApi } from '@/api/base/upload'
import { IconEnum } from '@/enums/appEnum'
import { message } from 'ant-design-vue';
// import { Loading, useLoading } from '/@/components/Loading';
import { getUserInfo } from '@/api/base/user'

let evtType = ''
let userData = {}
const deptId = ref<number>()
onMounted(async () => {
  const data = await getUserInfo()
  
  deptId.value = data.user.deptId
  userData = data.user
})

//表单内容
const preData = ref()
const preFlag = ref(false)
const preRef = ref()

const ypform = ref({})

const nameOptions = ref([])
const ops = ref()
async function getList(e) {
  const res = await getEvtnameData({pageSize:100,evtClass:e})  
  nameOptions.value = res.list
  ops.value = nameOptions.value.map(item=>({value:item.id,label:item.evtName}))
}

//事件内容
const schemas1: FormSchema[] = [
  {
    field: 'evtClass',
    component: 'Select',
    required: true,
    label: '事件分类',
    colProps: {
      span: 12,
    },
    componentProps:{
      options: getDictOptions(DICT_TYPE.EVT_CLASS,'string'),
      disabled:true,
    }
  },
  {
    label: '事件名称',
    required: true,
    field: 'evtname',
    component: 'Input',
    colProps: {
      span: 24,
    },
    componentProps:{
      disabled:true,
    },
  },
  {
    label: '事件名称',
    required: true,
    field: 'evtnameId',
    show:false,
    component: 'ApiSelect',
    colProps: {
      span: 24,
    },
    componentProps:{
      options: ops,
      disabled:true,
    },
  },
  {
    field: 'evTime',
    component: 'DatePicker',
    required: true,
    label: '发生时间',
    colProps: {
      span: 24,
    },
    componentProps: {
      showTime:true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    }
  },
  {
    field: 'evDeptId',
    component: 'ApiTreeSelect',
    label: '发生科室',
    required: true,
    componentProps: { 
      api: () => listSimpleDept(),
      handleTree:'id',
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'evtLevel',
    required: true,
    component: 'Select',
    label: '事件级别',
    colProps: {
      span: 12,
    },
    componentProps: {
      options: getDictOptions(DICT_TYPE.EVT_LEVEL,'string'),
    },
  },
  {
    field: 'evtCritical',
    component: 'Select',
    // required: true,
    label: '伤害程度',
    componentProps: {
      options: getDictOptions(DICT_TYPE.evt_pat_level,'string'),
    },
    colProps: {
      span: 12,
    },
  },
]
//本地暂时存储
watch(preData,(val)=>{
  sessionStorage.setItem('preData',JSON.stringify(val))
},{deep: true})


// 深拷贝
const deepClone = (data) => {
  const type = getObjType(data)
  let obj
  if (type === 'array') {
    obj = []
  } else if (type === 'object') {
    obj = {}
  } else {
    //不再具有下一层次
    return data
  }
  if (type === 'array') {
    for (let i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (const key in data) {
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}
const getObjType = (obj) => {
  const toString = Object.prototype.toString
  const map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object',
  }
  if (obj instanceof Element) {
    return 'element'
  }
  return map[toString.call(obj)]
}
defineOptions({ name: 'InfoModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)
const isEvent = ref(false)
// const [registerForm, { setFieldsValue, resetFields, resetSchema, validate }] = useForm({
//   labelWidth: 120,
//   baseColProps: { span: 24 },
//   schemas: createFormSchema,
//   showActionButtonGroup: false,
//   actionColOptions: { span: 23 },
// })
const  [form2, {  validate:vdate2,resetFields:resetF2,resetSchema:resetS2,updateSchema:update2,setFieldsValue:setValue2 }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: schemas1,
  showActionButtonGroup: false,
  actionColOptions: { span: 24 },
})
const [form1, { validate:vdate1, }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: patient,
  actionColOptions: { span: 24 },
  showActionButtonGroup: false,
})
const  [form3, {  validate:vdate3,resetFields:resetF3 }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: schemas3,
  showActionButtonGroup: false,
  actionColOptions:{ span: 24 }
})
const  [form4, {  getFieldsValue:getV4, }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: evtData,
  showActionButtonGroup: false,
  actionColOptions:{ span: 24 }
})

let itemId = ''
let itemData = {}
const itemStatus = ref(false)
const isYaoPin = ref(false)
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false,maskClosable:false, })

   
  activeKey.value = ['1']
  isEvent.value = !!data?.isEvent
  if(data.record && data.record.status == '1'){
    itemStatus.value = true
  }else{
    itemStatus.value = false
  }
  if (unref(isEvent)) {
    itemId = data.record.id
    itemData = data.record
    // 患者信息
    // resetSchema(patient)
    const res1 = await getInfoPatient(itemId)
    patientdata.value = res1
    ypform.value = res1
    
    if(patientdata.value == null){
      addflag.value = false
    }else{
      addflag.value = true
    }

    //事件内容 上报人信息
    const res3 = await getInfo(itemId)
    formdata1.value = res3
    resetS2(schemas1)
    formdata3.value = res3
    formdata3.value.repDeptId = deptId.value
    

    await getEvtcontent(data.record.evtcontentId).then(res=>{
      if(/国家/.test(itemData.evtname) && /药品/.test(itemData.evtname)){
        isYaoPin.value = true
      }else{
        isYaoPin.value = false
      }
      
      preData.value = res
      preData.value.forEach(item=>{
        if(item.type == '2'){
          if(item.value == null){
            item.value = []
          }else{
            item.value = item.value.split(',')
          }
        }else if(item.type == '9'){
          if(item.value == null){
            item.tableData = []
          }else{
            item.value = item.value.join(',')
          }
        }
      })
      preFlag.value = true
    })


    const res6 = await findSysQuestions({infoId:itemId})
    setTimeout(()=>{
      if(res6.length){
        res6.forEach(item=>{
          if(item.type == '2'){
            if(item.value == null){
              item.value = []
            }else{
              item.value = item.value.split(',')
            }
          }else if(item.type == '6'){
            if(item.value !== null){
              item.value = parseInt(item.value)
            }
          }else{
            item.value = item.value
          }
          if(item.type == '9'){
            item.tableData = item.tableData
          }
          if(preData.value){
            preData.value.forEach(it=>{
              if(item.id == it.id){
                it.value = item.value
              }
            })
          }
        })
        // console.log(res6,111);
        // console.log(preData.value,222);
        
        // preData.value = res6
        preFlag.value = true    
      }
    },500)
      
    // 对策知识库
    const res4 = await findTreeInfoCause({infoId:itemId})
    if(res4){
      treeLeft.value = res4.treeData
      huixianId = res4.nodeIds
    }

    //措施知识库
    const res5 = await findTreeInfoMeasures({infoId:itemId})
    if(res5){
      treeRight.value = res5.treeData
    }
    

  }else{
    itemStatus.value = false
    isEvent.value = false;
    addModel.value = true;
  }
})

var huixianId = []

//左侧锚点
const items = ref([
  {
    key: '1',
    href: '#p1',
    title: '事件内容',
  },
  {
    key: '2',
    href: '#p2',
    title: '患者信息',
  },
  {
    key: '3',
    href: '#p3',
    title: '责任人信息',
  },
  {
    key: '4',
    href: '#p4',
    title: '上报人信息',
  },

])

const activeKey = ref(['1']);
const activeKeyRight = ref(['1']);

const formRef1 = ref();
const formRef2 = ref();
const formRef4 = ref();
const formRef5 = ref();
const patientdata = ref<any>({});//查询事件上报内容明细列表
const addflag = ref(false)

const formdata1 = ref<any>({});//事件内容

const formdata3 = ref<any>({});//上报人信息


const containerRef = ref<HTMLElement | null>(null)
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
const rightRef = ref<HTMLElement | null>(null)


//原因对策分析

const treeRef1 = ref()
const treeRef2 = ref()
const treeLeftRef = ref()
const treeRightRef = ref()

const treeLeft = ref()//外面tree数据
const treeRight = ref()
const prop1 = {
  label: 'causeName',
}
const filterText1 = ref('')
watch(filterText1, (val) => {
  treeRef1.value!.filter(val)
})

const filterNode1 = (value: string, data: Tree) => {
  if (!value) return true
  return data.causeName.includes(value)
}

const prop2 = {
  label: 'measuresName',
}
const filterText2 = ref('')
watch(filterText2, (val) => {
  treeRef2.value!.filter(val)
})

const filterTextLeft = ref('')
watch(filterTextLeft, (val) => {
  treeLeftRef.value!.filter(val)
})
const filterTextRight = ref('')
watch(filterTextRight, (val) => {
  treeRightRef.value!.filter(val)
})

const filterNode2 = (value: string, data: Tree) => {
  if (!value) return true
  return data.measuresName.includes(value)
}


// table
const [rTable1, { reload:reload1,getDataSource:getData1 }] = useTable({
  maxHeight:500,
  beforeFetch:(parms) => { 
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoResponsiblePersonsPage,
  columns:tableData1,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  
})
const [rTable2, { reload:reload2,getDataSource:getData2 }] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoDiscussantPage,
  maxHeight:500,
  rowClassName:getRowClassName,
  columns:tableData2,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  tableSetting: { fullScreen: true },
  
})

const [rTable5, { reload:reload5}] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoAssignmentPage,
  maxHeight:500,
  columns:tableData5,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  
})


function handleChange (list: string[],fileList,event) {
  console.log(list,fileList,event);
  createMessage.info(`已上传文件`)
}
function fileListCg (fileList: string[]) {
  console.log(fileList);
}
const fileList = ref([])

const rowClassName = ref()
function rowClick(record, index, event){  
  record.isAbsent = '1';
  updateInfoDiscussant({...record}).then(res=>{
    message.success('成功')
    reload2()
  })
  rowClassName.value = index;
}
function getRowClassName(record, index) {
    return record.isAbsent == 1 ? "tableRowStyle" : "";
}

const addModel = ref(false)

function onClickfd(lr){
  let lefte = document.querySelector('.left')
  let righte = document.querySelector('.right')
  console.log(lefte,righte);
  if(lr =='left'){
    lefte.style.width = '90%'
    righte.style.width = '10%'
    activeKeyRight.value = []
  }else if(lr =='right'){
    lefte.style.width = '10%'
    righte.style.width = '90%'
    activeKey.value = []
  }else{
    lefte.style.width = '50%'
    righte.style.width = '50%'
  }
}

//压疮


const [preTableReg, { reload:reloadPre,getDataSource:getYcData }] = useTable({
  beforeFetch:(parms)=>{
    parms.infoId = itemId
    if(!parms.infoId){
      parms.infoId = '0'
    }else{
      parms.infoId = itemId
    }
  },
  api:getInfoPressureSorePage,
  title:"压力性损伤部位",
  maxHeight:400,
  columns:preTable,
  pagination:{ pageSize: 10 },
  showIndexColumn:false,
  
})


</script>
<template>
  <div>
    <BasicModal  v-bind="$attrs" :title="isEvent ? `事件上报  （${itemData.evtname} & ${itemData.id}）` : t('action.create') "
      @register="registerModal" @ok="handleSubmit" :defaultFullscreen="true" :showOkBtn="false" okText="提交" >
      <div v-if="isEvent" class="container1">
        <div class="left" id="container" ref="containerRef">
          <div style="width: 100%;">
            <el-affix :offset="60">
              <el-anchor :container="containerRef" type="default" :offset="30" direction="horizontal" @click="handleClick">
                <div v-for=" (it,index) in items" v-if="isYaoPin">
                  <el-anchor-link class="leftLabel" :href="it.href" :title="it.title" v-if="index<1" />
                </div>
                <div v-for=" (it,index) in items" v-else>
                  <el-anchor-link class="leftLabel" :href="it.href" :title="it.title" />
                </div>
              </el-anchor>
            </el-affix>
            <a-collapse v-model:activeKey="activeKey">
              <a-collapse-panel class="custom-panel" key="1" header="事件内容" id="p1">
                <BasicForm :schemas="schemas1" :model="formdata1" :disabled="true" @register="form2" ref="formRef2" />
                
                <el-form :disabled="true" v-if="preFlag && !isYaoPin" ref="preRef" style="max-width: 100%;padding: 10px;color: rgba(0, 0, 0, 0.88);" :model="preData" label-width="180" label-position="top">
                  <div v-for="(element, index) in preData" :key="index + 'b'" class="pre">
                    <el-form-item :label="element.name" :key="index" v-if="element.type == '1'"
                      :required="element.isRequire == '0' ? true : false" >
                      <el-radio-group v-model="element.value">
                        <el-radio v-for="(item, oIndex) in element.options" :key="item.id" :label="item.name" :value="item.id"
                          size="large"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="element.type == '2'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-checkbox-group v-model="element.value">
                        <el-checkbox v-for="(item) in element.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item v-if="element.type == '3'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-input v-model="element.value" style="width: 240px" placeholder="输入内容" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '4'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-input v-model="element.value" style="width: 240px" :autosize="{ minRows: 2, maxRows: 4 }"
                        type="textarea" placeholder="输入内容" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '5'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-select v-model="element.value" placeholder="请选择" style="width: 240px">
                        <el-option v-for="(item) in element.options" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </el-form-item>
                    <el-form-item v-if="element.type == '6'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <a-input-number :disabled="true" v-model:value="element.value" :min="element.min" :max="element.max" style="min-width:140px;width: 40%;" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '7'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-date-picker v-model="element.value" type="datetime" placeholder="选择时间" />
                    </el-form-item>
                    <el-form-item v-if="element.type == '9'" :label="element.name" :key="index"
                      :required="element.isRequire == '0' ? true : false">
                      <el-table :data="element.tableData" style="width: 100%" max-height="300">
                        <el-table-column v-for="(item, oIndex) in element.options" :key="oIndex" :prop="item.prop" :label="item.name">
                          <template #default="scope">
                            <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </div>

                </el-form>

                <BasicTable v-if="itemData.isPressureSore == '0'" @register="preTableReg" >
                </BasicTable>

                <!-- 药品 -->
                <el-form disabled ref="ypRef" v-if="isYaoPin" style="width: 100%;padding: 10px;color: rgba(0, 0, 0, 0.88);" :model="preData" size="small">
                  <el-form-item style="align-items:center" label="病人类型" required>
                    <el-radio-group v-model="ypform.patCometype">
                      <el-radio v-for="(item, oIndex) in getDictOptions(DICT_TYPE.EVT_PATIENT_TYPE,'string')"  :label="item.label" :value="item.value"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[0].name" :key="index" v-if="preData[0].type == '1'"
                    :required="preData[0].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[0].value">
                      <el-radio v-for="(item, oIndex) in preData[0].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[1].name" :key="index" v-if="preData[1].type == '1'"
                    :required="preData[1].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[1].value">
                      <el-radio v-for="(item, oIndex) in preData[1].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="align-items:center" :label="preData[2].name" :key="index" v-if="preData[2].type == '1'"
                    :required="preData[2].isRequire == '0' ? true : false" >
                    <el-radio-group v-model="preData[2].value">
                      <el-radio v-for="(item, oIndex) in preData[2].options" :key="item.id" :label="item.name" :value="item.id"
                        size="large"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <table class="table">
                      <tr>
                        <td colspan="1">
                          <el-form-item label="患者姓名" required>
                            <el-input style="min-width:80px;" v-model="ypform.patName" placeholder="输入患者姓名" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item label="性别" required>
                            <a-radio-group v-model:value="ypform.patSex">
                              <a-radio value="1">男</a-radio>
                              <a-radio value="2">女</a-radio>
                            </a-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item label="年龄" required >
                            <div>年 <a-input-number disabled style="width:70%;" size="small" v-model:value="ypform.patNewYear" :min="0"/></div>
                            <div>月 <a-input-number disabled style="width:70%;" size="small" v-model:value="ypform.patNewMonth" :min="1" :max="12" /></div>
                            <div>日 <a-input-number disabled style="width:70%;" size="small" v-model:value="ypform.patNewDay" :min="0" :max="31" /></div>
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[3].type == '3'" :label="preData[3].name"
                            :required="preData[3].isRequire == '0' ? true : false">
                            <el-input v-model="preData[3].value" placeholder="患者民族" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[4].type == '6'" :label="preData[4].name"
                            :required="preData[4].isRequire == '0' ? true : false">
                            <!-- <el-input-number v-model="preData[4].value" :min="preData[4].min" :max="preData[4].max"  @change="changeNum" /> -->
                            <a-input-number disabled size="small" v-model:value="preData[4].value" :min="preData[4].min" :max="preData[4].max" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[5].type == '3'" :label="preData[5].name"
                            :required="preData[5].isRequire == '0' ? true : false">
                            <el-input v-model="preData[5].value" placeholder="患者联系方式" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="2">
                          <el-form-item v-if="preData[6].type == '3'" :label="preData[6].name"
                            :required="preData[6].isRequire == '0' ? true : false">
                            <el-input v-model="preData[6].value" placeholder="原患疾病" />
                          </el-form-item>
                        </td>

                        <td colspan="2">
                          <el-form-item label="医院名称">
                            <el-input v-model="userData.orgName" />
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item label="病历/门诊号" required>
                            <el-input v-model="ypform.patMedrecno" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="7">
                          
                          <el-form-item :label="preData[7].name" :key="index" v-if="preData[7].type == '1'"
                            :required="preData[7].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[7].value">
                              <el-radio v-for="(item, oIndex) in preData[7].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="7">
                          
                          <el-form-item :label="preData[8].name" :key="index" v-if="preData[8].type == '1'"
                            :required="preData[8].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[8].value">
                              <el-radio v-for="(item, oIndex) in preData[8].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="7">
                          <el-form-item v-if="preData[9].type == '2'" :label="preData[9].name" :key="index"
                            :required="preData[9].isRequire == '0' ? true : false">
                            <el-checkbox-group v-model="preData[9].value">
                              <el-checkbox v-for="(item) in preData[9].options" :key="item.id" :label="item.name" :value="item.id" />
                            </el-checkbox-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[10].type == '9'" :label="preData[10].name" :key="index"
                            :required="preData[10].isRequire == '0' ? true : false">
                            <el-table :data="preData[10].tableData" style="width: 100%" max-height="300">
                              <el-table-column v-for="(item, oIndex) in preData[10].options" :key="oIndex" :prop="item.prop" :label="item.name">
                                <template #default="scope">
                                  <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                                </template>
                              </el-table-column>
                            </el-table>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          
                          <el-form-item v-if="preData[11].type == '9'" :label="preData[11].name" :key="index"
                            :required="preData[11].isRequire == '0' ? true : false">
                            <el-table :data="preData[11].tableData" style="width: 100%" max-height="300">
                              <el-table-column v-for="(item, oIndex) in preData[11].options" :key="oIndex" :prop="item.prop" :label="item.name">
                                <template #default="scope">
                                  <el-input v-model="scope.row[item.id]" placeholder="输入内容" />
                                </template>
                              </el-table-column>
                            </el-table>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="3">
                          <el-form-item v-if="preData[12].type == '3'" :label="preData[12].name"
                            :required="preData[12].isRequire == '0' ? true : false">
                            <el-input v-model="preData[12].value" placeholder="输入" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item v-if="preData[13].type == '7'" :label="preData[13].name"
                            :required="preData[13].isRequire == '0' ? true : false">
                            <el-date-picker v-model="preData[13].value" type="datetime" placeholder="不良反应/事件发生时间" locale="locale" />
                          </el-form-item>
                        </td>
                      </tr>

                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[14].type == '4'" :label="preData[14].name" :key="index"
                            :required="preData[14].isRequire == '0' ? true : false">
                            <el-input v-model="preData[14].value" :autosize="{ minRows: 2, maxRows: 4 }"
                              type="textarea" placeholder="输入不良事件反应/事件过程描述" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[15].name" :key="index" v-if="preData[15].type == '1'"
                            :required="preData[15].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[15].value">
                              <el-radio v-for="(item, oIndex) in preData[15].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[18].name" :key="index" v-if="preData[18].type == '1'"
                            :required="preData[18].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[18].value">
                              <el-radio v-for="(item, oIndex) in preData[18].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[19].name" :key="index" v-if="preData[19].type == '1'"
                            :required="preData[19].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[19].value">
                              <el-radio v-for="(item, oIndex) in preData[19].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[20].name" :key="index" v-if="preData[20].type == '1'"
                            :required="preData[20].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[20].value">
                              <el-radio v-for="(item, oIndex) in preData[20].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1" rowspan="2">
                          <el-form-item label="关联性评价"></el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item :label="preData[21].name" :key="index" v-if="preData[21].type == '1'"
                            :required="preData[21].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[21].value">
                              <el-radio v-for="(item, oIndex) in preData[21].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[22].type == '3'" :label="preData[22].name"
                            :required="preData[22].isRequire == '0' ? true : false">
                            <el-input v-model="preData[22].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="3">
                          <el-form-item :label="preData[23].name" :key="index" v-if="preData[23].type == '1'"
                            :required="preData[23].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[23].value">
                              <el-radio v-for="(item, oIndex) in preData[23].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[24].type == '3'" :label="preData[24].name"
                            :required="preData[24].isRequire == '0' ? true : false">
                            <el-input v-model="preData[24].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1" rowspan="2">
                          <el-form-item label="报告人信息"></el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[25].type == '3'" :label="preData[25].name" :key="index"
                            :required="preData[25].isRequire == '0' ? true : false">
                            <el-input v-model="preData[25].value" placeholder="报告人联系电话" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item :label="preData[26].name" :key="index" v-if="preData[26].type == '1'"
                            :required="preData[26].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[26].value">
                              <el-radio v-for="(item, oIndex) in preData[26].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2">
                          <el-form-item v-if="preData[27].type == '3'" :label="preData[27].name"
                            :required="preData[27].isRequire == '0' ? true : false">
                            <el-input v-model="preData[27].value" placeholder="报告联系人电子邮箱" />
                          </el-form-item>
                        </td>
                        <td colspan="3">
                          <el-form-item v-if="preData[28].type == '3'" :label="preData[28].name"
                            :required="preData[28].isRequire == '0' ? true : false">
                            <el-input v-model="preData[28].value" placeholder="签名" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="1">
                          <el-form-item label="报告单位信息 "></el-form-item>
                        </td>
                        <td colspan="2">
                          <el-form-item v-if="preData[29].type == '3'" :label="preData[29].name"
                            :required="preData[29].isRequire == '0' ? true : false">
                            <el-input v-model="preData[29].value" placeholder="单位名称" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[30].type == '3'" :label="preData[30].name"
                            :required="preData[30].isRequire == '0' ? true : false">
                            <el-input v-model="preData[30].value" placeholder="单位联系人" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[31].type == '3'" :label="preData[31].name" 
                            :required="preData[31].isRequire == '0' ? true : false">
                            <el-input v-model="preData[31].value" placeholder="单位联系人电话" />
                          </el-form-item>
                        </td>
                        <td colspan="1">
                          <el-form-item v-if="preData[32].type == '7'" :label="preData[32].name"
                            :required="preData[32].isRequire == '0' ? true : false">
                            <el-date-picker v-model="preData[32].value" type="datetime" placeholder="选择时间" locale="locale" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item :label="preData[33].name" :key="index" v-if="preData[33].type == '1'"
                            :required="preData[33].isRequire == '0' ? true : false" >
                            <el-radio-group v-model="preData[33].value">
                              <el-radio v-for="(item, oIndex) in preData[33].options" :key="item.id" :label="item.name" :value="item.id"
                                size="large"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item v-if="preData[34].type == '4'" :label="preData[34].name"
                            :required="preData[34].isRequire == '0' ? true : false">
                            <el-input v-model="preData[34].value" :autosize="{ minRows: 2, maxRows: 4 }"
                              type="textarea" placeholder="输入内容" />
                          </el-form-item>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6">
                          <el-form-item label="附件">
                            <!-- <BasicUpload :maxSize="20" :maxNumber="10" @change="handleChange" :value="fileList" :api="uploadApi" /> -->
                          </el-form-item>
                        </td>
                      </tr>
                  </table>
                </el-form> 
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="2" header="患者信息" id="p2" v-show="!isYaoPin" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicForm :schemas="patient" ref="formRef1" :model="patientdata" @register="form1" :disabled="true" />
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="3" header="责任人信息" id="p3" v-show="!isYaoPin" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicTable @register="rTable1" :disabled="true">
                
                </BasicTable>
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="4" header="上报人信息" id="p4" v-show="!isYaoPin" :collapsible="isYaoPin ? 'disabled' : ''" >
                <BasicForm :schemas="schemas3" :model="formdata3" @register="form3" ref="formRef4" :disabled="true" />
              </a-collapse-panel>
            </a-collapse>
          </div>
  
        </div>
        <div class="right " id="rightRef" ref="rightRef">
          <div style="width: 100%;">
            <el-affix :offset="60">
              <el-anchor :container="rightRef" type="default" :offset="30" direction="horizontal" @click="handleClick">
                <el-anchor-link class="rightLabel" href="#r1" title="事件分析" />
                <el-anchor-link class="rightLabel" href="#r2" title="事件整改" />
              </el-anchor>
            </el-affix>
            <a-collapse v-model:activeKey="activeKeyRight">
              <a-collapse-panel class="custom-panel" key="1" header="事件分析" id="r1">
                <p class="pTitle">参会时间/地点/人员</p>
                <BasicForm :model="formdata3" ref="formRef5" @register="form4" :disabled="true" />
                <BasicTable title="参会人员" class="rTable2" @register="rTable2" >
                 
                </BasicTable>
  
                <p class="pTitle">原因对策分析</p>
                <a-row style="height: 100%;min-height: 300px;" :gutter="16">
                  <a-col style="height: 100%;" :span="12">
                    <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="原因知识库" :bordered="false">
                      
                      <el-input v-model="filterTextLeft" style="width: 100%" placeholder="搜索" />
                      <el-tree class="treeStyle" ref="treeLeftRef" :data="treeLeft" node-key="id" :expand-on-click-node="true"
                       :filter-node-method="filterNode1" :props="prop1" default-expand-all highlight-current />
                    </a-card>
                  </a-col>
                  <a-col style="height: 100%;" :span="12">
                    <a-card style="height: 100%;" :bodyStyle="{ padding: '10px 24px' }" title="对策知识库" :bordered="false">
                      
                      <el-input v-model="filterTextRight" style="width: 100%" placeholder="搜索" />
                      <el-tree class="treeStyle" ref="treeRightRef" :data="treeRight" node-key="id"
                        :expand-on-click-node="false" :filter-node-method="filterNode2" :props="prop2" default-expand-all />
                    </a-card>
                  </a-col>
                </a-row>
<!--   
                <p class="pTitle">附件上传</p>
                <BasicUpload :maxSize="20" :maxNumber="10" @change="handleChange" :beforereviewData="fileListCg" :value="fileList"
                    :empty-hide-preview="true" :api="uploadApi" />
                     -->
                <p class="pTitle">会议总结</p>
                <a-textarea disabled v-model:value="formdata3.sumnotes" placeholder="请输入内容" :rows="4" />
  
  
              </a-collapse-panel>
              <a-collapse-panel class="custom-panel" key="2" header="事件整改" id="r2">
                <BasicTable title="对策措施整改" :columns="tableData5" :loading="loading" :pagination="{ pageSize: 20 }"
                  @register="rTable5">
                </BasicTable>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </div>
  
      </div>
      <template #insertFooter>
        <div style="float: left;margin-left: 20px;">
          <a-button type="primary" @click="onClickfd('left')" > 左 </a-button>
          <a-button type="primary" @click="onClickfd('right')" > 右 </a-button>
          <a-button type="primary" @click="onClickfd('center')" > 居中 </a-button>
        </div>
      </template>
      <!-- <BasicForm v-else @register="registerForm" /> -->
    </BasicModal>
    
  </div>
</template>
<style lang="less" scoped>
.table{
  min-width: 50%;
  max-width: 100%;
  vertical-align: middle;
  margin-top:20px;
  tr{
    border: 1px solid #000;
    vertical-align: middle;
    td{
      .el-form-item{
        margin: 6px 0;
        padding:0 2px;
        align-items: center;
      }
      ::v-deep(:where(.css-dev-only-do-not-override-12ru88o).ant-form-item) {
        margin: 6px 0;
      }
      border: 1px solid #000;
      vertical-align: middle
    }
  }
}
.pTitle{
  font-size: 16px;
  font-weight: bold;
  padding: 10px 0 0 0;
}
::v-deep(.tableRowStyle){
  td{
    background-color: rgba(255, 157, 101, 0.438);
  }
}
.rTable2 ::v-deep(.ant-table-tbody .ant-table-row:hover > td){
  background-color: rgba(255, 157, 101, 0.438)!important;
}
.custom-panel ::v-deep(.ant-collapse-header) {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
// .el-anchor ::v-deep(.el-anchor__link.is-active) {
//   background-color: #409eff; /* 蓝色背景 */
//   color:red ; /* 白色文字 */
// }
.treeStyle {
  max-width: 600px;
  height: 400px;
  overflow: scroll;
  overflow-x: visible;
}
.lrFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.container1 {
  display: flex;
  justify-content: space-between;
  height: 100%;
  width: 100%;

  .left {
    display: flex;
    width: 50%;
    height: 100%;
    overflow-x: scroll;
    .leftLabel {
      // width: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;

      :deep(a) {
        color: #000;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }

  .right {
    display: flex;
    width: 50%;
    height: 100%;
    overflow-x: scroll;
    padding-left: 5px;

    .rightLabel {
      // width: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;

      :deep(a) {
        color: #000;
        font-weight: bold;
        font-size: 18px;
      }
    }

    
  }
}


.scale-down-hor-left {
      -webkit-animation: scale-down-hor-left 0.4s both;
              animation: scale-down-hor-left 0.4s both;
    }
    @-webkit-keyframes scale-down-hor-left {
      0% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
      100% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
    }
    @keyframes scale-down-hor-left {
      0% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
      100% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
    }
    .scale-down-hor-right {
      -webkit-animation: scale-down-hor-right 0.4s both;
              animation: scale-down-hor-right 0.4s both;
    }

    @-webkit-keyframes scale-down-hor-right {
      0% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
      100% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
      }
    }
    @keyframes scale-down-hor-right {
      0% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
      }
      100% {
        -webkit-transform: scaleX(1);
                transform: scaleX(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
      }
    }


</style>