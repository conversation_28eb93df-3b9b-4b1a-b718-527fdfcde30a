/**
 * 事件分析数据验证和格式化工具
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-27
 */

import { message } from 'ant-design-vue'

/**
 * 验证事件ID是否有效
 */
export function validateEventId(eventId: string): boolean {
  if (!eventId || typeof eventId !== 'string' || eventId.trim() === '') {
    console.error('❌ [DataValidation] 事件ID无效:', eventId)
    return false
  }
  return true
}

/**
 * 验证API响应数据
 */
export function validateApiResponse(response: any, requiredFields: string[] = []): boolean {
  if (!response) {
    console.error('❌ [DataValidation] API响应为空')
    return false
  }

  // 检查必需字段
  for (const field of requiredFields) {
    if (!(field in response)) {
      console.warn(`⚠️ [DataValidation] 缺少必需字段: ${field}`)
    }
  }

  return true
}

/**
 * 安全获取对象属性值
 */
export function safeGet(obj: any, path: string, defaultValue: any = ''): any {
  try {
    const keys = path.split('.')
    let result = obj
    
    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key]
      } else {
        return defaultValue
      }
    }
    
    return result !== null && result !== undefined ? result : defaultValue
  } catch (error) {
    console.warn(`⚠️ [DataValidation] 获取属性失败: ${path}`, error)
    return defaultValue
  }
}

/**
 * 格式化文件信息
 */
export function formatFileInfo(file: any): any {
  if (!file) return null
  
  return {
    id: safeGet(file, 'id') || safeGet(file, 'fileId'),
    fileName: safeGet(file, 'name') || safeGet(file, 'fileName'),
    fileSize: safeGet(file, 'size', 0) || safeGet(file, 'fileSize', 0),
    fileType: safeGet(file, 'type') || safeGet(file, 'fileType'),
    url: safeGet(file, 'url') || safeGet(file, 'fileUrl'),
    createTime: safeGet(file, 'createTime') || safeGet(file, 'uploadTime')
  }
}

/**
 * 格式化动态字段数据
 */
export function formatDynamicField(field: any, index: number): any {
  if (!field) return null
  
  const fieldKey = safeGet(field, 'key') || 
                   safeGet(field, 'evtquestionId') || 
                   safeGet(field, 'id') || 
                   `field_${index}`
                   
  const fieldLabel = safeGet(field, 'label') || 
                     safeGet(field, 'evtquestionName') || 
                     safeGet(field, 'questionName') || 
                     `字段${index + 1}`
                     
  const fieldValue = safeGet(field, 'value') || 
                     safeGet(field, 'optiontext') || 
                     safeGet(field, 'cellvalue') || 
                     safeGet(field, 'answer') || 
                     ''
                     
  const fieldType = safeGet(field, 'type') || 
                    safeGet(field, 'fieldType') || 
                    safeGet(field, 'questionType') || 
                    'text'

  return {
    key: fieldKey,
    label: fieldLabel,
    value: fieldValue,
    fieldType: fieldType,
    isTable: safeGet(field, 'isTable', false) || safeGet(field, 'isTableField', false),
    hasValue: !!(fieldValue && fieldValue.toString().trim() !== ''),
    isEmpty: !(fieldValue && fieldValue.toString().trim() !== ''),
    order: safeGet(field, 'order', index) || safeGet(field, 'sort', index) || safeGet(field, 'sequence', index)
  }
}

/**
 * 处理API错误
 */
export function handleApiError(error: any, context: string): string {
  let errorMessage = '未知错误'
  
  if (error instanceof Error) {
    errorMessage = error.message
  } else if (typeof error === 'string') {
    errorMessage = error
  } else if (error && error.message) {
    errorMessage = error.message
  }
  
  console.error(`❌ [DataValidation] ${context}失败:`, {
    error,
    message: errorMessage,
    stack: error instanceof Error ? error.stack : undefined
  })
  
  return errorMessage
}

/**
 * 显示数据加载统计信息
 */
export function logDataLoadingStats(context: string, stats: {
  total: number
  success: number
  failed: number
  details?: any
}) {
  const successRate = stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0
  
  console.log(`📊 [DataValidation] ${context}加载统计:`, {
    总数: stats.total,
    成功: stats.success,
    失败: stats.failed,
    成功率: `${successRate}%`,
    详情: stats.details
  })
  
  // 根据成功率显示不同的提示
  if (stats.failed === 0) {
    console.log(`✅ [DataValidation] ${context}全部加载成功`)
  } else if (stats.success > 0) {
    console.warn(`⚠️ [DataValidation] ${context}部分加载失败 (${stats.failed}/${stats.total})`)
  } else {
    console.error(`❌ [DataValidation] ${context}全部加载失败`)
  }
}

/**
 * 创建重试函数
 */
export function createRetryFunction<T>(
  fn: () => Promise<T>,
  maxRetries: number = 2,
  delay: number = 1000,
  context: string = '操作'
): () => Promise<T> {
  return async () => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn()
        if (attempt > 1) {
          console.log(`✅ [DataValidation] ${context}在第${attempt}次尝试后成功`)
        }
        return result
      } catch (error) {
        if (attempt === maxRetries) {
          console.error(`❌ [DataValidation] ${context}在${maxRetries}次尝试后仍然失败:`, error)
          throw error
        }
        
        console.log(`⏳ [DataValidation] ${context}第${attempt}次尝试失败，${delay}ms后重试...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw new Error(`${context}重试失败`)
  }
}

/**
 * 验证并格式化患者信息
 */
export function formatPatientInfo(patientInfo: any): any {
  if (!patientInfo) return null
  
  return {
    id: safeGet(patientInfo, 'id'),
    infoId: safeGet(patientInfo, 'infoId'),
    patName: safeGet(patientInfo, 'patName'),
    patSex: safeGet(patientInfo, 'patSex'),
    patYear: safeGet(patientInfo, 'patYear'),
    patHosnumber: safeGet(patientInfo, 'patHosnumber'),
    patMedrecno: safeGet(patientInfo, 'patMedrecno'),
    patDiagnosis: safeGet(patientInfo, 'patDiagnosis'),
    patCometype: safeGet(patientInfo, 'patCometype'),
    patBedno: safeGet(patientInfo, 'patBedno'),
    patAdmissiondate: safeGet(patientInfo, 'patAdmissiondate'),
    patGradeOfCare: safeGet(patientInfo, 'patGradeOfCare'),
    patOccupation: safeGet(patientInfo, 'patOccupation'),
    patEdulevel: safeGet(patientInfo, 'patEdulevel'),
    patAttent: safeGet(patientInfo, 'patAttent'),
    patOther: safeGet(patientInfo, 'patOther')
  }
}

/**
 * 检查数据完整性
 */
export function checkDataIntegrity(data: any, requiredFields: string[]): {
  isValid: boolean
  missingFields: string[]
  warnings: string[]
} {
  const missingFields: string[] = []
  const warnings: string[] = []
  
  for (const field of requiredFields) {
    const value = safeGet(data, field)
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(field)
    }
  }
  
  if (missingFields.length > 0) {
    warnings.push(`缺少必需字段: ${missingFields.join(', ')}`)
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings
  }
}
