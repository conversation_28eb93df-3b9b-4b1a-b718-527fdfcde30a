<template>
  <div class="cycle-info-form">
    <a-form :model="formData" layout="vertical" ref="formRef">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item 
            label="循环名称" 
            name="name"
            :rules="[{ required: true, message: '请输入循环名称' }]"
          >
            <a-input 
              v-model:value="formData.name" 
              placeholder="请输入循环名称"
              :readonly="readonly"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="循环状态" name="status">
            <a-select 
              v-model:value="formData.status" 
              placeholder="请选择状态"
              :disabled="readonly"
            >
              <a-select-option value="active">进行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="paused">已暂停</a-select-option>
              <a-select-option value="cancelled">已取消</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="循环描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入循环描述"
          :rows="3"
          :readonly="readonly"
        />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="开始日期" name="startDate">
            <a-date-picker
              v-model:value="formData.startDate"
              style="width: 100%"
              :disabled="readonly"
              :value="ensureDayjsObject(formData.startDate)"
              @change="(date) => formData.startDate = ensureDayjsObject(date)"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="结束日期" name="endDate">
            <a-date-picker
              v-model:value="formData.endDate"
              style="width: 100%"
              :disabled="readonly"
              :value="ensureDayjsObject(formData.endDate)"
              @change="(date) => formData.endDate = ensureDayjsObject(date)"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="预算" name="budget">
            <a-input-number 
              v-model:value="formData.budget" 
              placeholder="请输入预算"
              style="width: 100%"
              :precision="2"
              :min="0"
              :readonly="readonly"
            >
              <template #addonAfter>元</template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="实际成本" name="actualCost">
            <a-input-number 
              v-model:value="formData.actualCost" 
              placeholder="请输入实际成本"
              style="width: 100%"
              :precision="2"
              :min="0"
              :readonly="readonly"
            >
              <template #addonAfter>元</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="团队成员" name="teamMembers">
        <div class="team-members-section">
          <div class="members-list">
            <div 
              v-for="(member, index) in formData.teamMembers" 
              :key="index"
              class="member-item"
            >
              <a-row :gutter="8" align="middle">
                <a-col :span="6">
                  <a-input 
                    v-model:value="member.userName" 
                    placeholder="姓名"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="6">
                  <a-input 
                    v-model:value="member.role" 
                    placeholder="角色"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="8">
                  <a-select
                    v-model:value="member.responsibilities"
                    mode="tags"
                    placeholder="职责"
                    style="width: 100%"
                    :disabled="readonly"
                  />
                </a-col>
                <a-col :span="3">
                  <a-input-number 
                    v-model:value="member.workload" 
                    placeholder="工作量%"
                    :min="0"
                    :max="100"
                    style="width: 100%"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="1">
                  <a-button 
                    type="text" 
                    danger 
                    size="small"
                    @click="removeMember(index)"
                    v-if="!readonly"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
          </div>
          
          <a-button 
            type="dashed" 
            @click="addMember"
            style="width: 100%; margin-top: 8px;"
            v-if="!readonly"
          >
            <template #icon><PlusOutlined /></template>
            添加团队成员
          </a-button>
        </div>
      </a-form-item>

      <a-form-item label="利益相关者" name="stakeholders">
        <a-select
          v-model:value="formData.stakeholders"
          mode="tags"
          placeholder="请输入利益相关者"
          style="width: 100%"
          :disabled="readonly"
        />
      </a-form-item>

      <a-form-item label="成功指标" name="successMetrics">
        <div class="success-metrics-section">
          <div class="metrics-list">
            <div 
              v-for="(metric, index) in formData.successMetrics" 
              :key="index"
              class="metric-item"
            >
              <a-row :gutter="8" align="middle">
                <a-col :span="6">
                  <a-input 
                    v-model:value="metric.name" 
                    placeholder="指标名称"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="4">
                  <a-select 
                    v-model:value="metric.type" 
                    placeholder="类型"
                    :disabled="readonly"
                  >
                    <a-select-option value="quantitative">定量</a-select-option>
                    <a-select-option value="qualitative">定性</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-input-number 
                    v-model:value="metric.targetValue" 
                    placeholder="目标值"
                    style="width: 100%"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="3">
                  <a-input 
                    v-model:value="metric.unit" 
                    placeholder="单位"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="6">
                  <a-input 
                    v-model:value="metric.measurementMethod" 
                    placeholder="测量方法"
                    :readonly="readonly"
                  />
                </a-col>
                <a-col :span="1">
                  <a-button 
                    type="text" 
                    danger 
                    size="small"
                    @click="removeMetric(index)"
                    v-if="!readonly"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
          </div>
          
          <a-button 
            type="dashed" 
            @click="addMetric"
            style="width: 100%; margin-top: 8px;"
            v-if="!readonly"
          >
            <template #icon><PlusOutlined /></template>
            添加成功指标
          </a-button>
        </div>
      </a-form-item>

      <a-form-item label="经验教训" name="lessonsLearned">
        <a-select
          v-model:value="formData.lessonsLearned"
          mode="tags"
          placeholder="请输入经验教训"
          style="width: 100%"
          :disabled="readonly"
        />
      </a-form-item>

      <a-form-item label="下一轮建议" name="nextCycleRecommendations">
        <a-select
          v-model:value="formData.nextCycleRecommendations"
          mode="tags"
          placeholder="请输入下一轮建议"
          style="width: 100%"
          :disabled="readonly"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

// Props定义
interface Props {
  cycle?: any
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits定义
const emit = defineEmits<{
  'update:cycle': [cycle: any]
}>()

// 响应式数据
const formRef = ref()
const formData = reactive({
  name: '',
  description: '',
  status: 'active',
  startDate: null,
  endDate: null,
  budget: null,
  actualCost: null,
  teamMembers: [] as any[],
  stakeholders: [] as string[],
  successMetrics: [] as any[],
  lessonsLearned: [] as string[],
  nextCycleRecommendations: [] as string[]
})

// 初始化表单数据
function initFormData() {
  if (props.cycle) {
    Object.assign(formData, {
      name: props.cycle.name || '',
      description: props.cycle.description || '',
      status: props.cycle.status || 'active',
      startDate: props.cycle.startDate ? ensureDayjsObject(props.cycle.startDate) : null,
      endDate: props.cycle.endDate ? ensureDayjsObject(props.cycle.endDate) : null,
      budget: props.cycle.budget || null,
      actualCost: props.cycle.actualCost || null,
      teamMembers: props.cycle.teamMembers || [],
      stakeholders: props.cycle.stakeholders || [],
      successMetrics: props.cycle.successMetrics || [],
      lessonsLearned: props.cycle.lessonsLearned || [],
      nextCycleRecommendations: props.cycle.nextCycleRecommendations || []
    })
  }
}

// 团队成员管理
function addMember() {
  formData.teamMembers.push({
    userId: '',
    userName: '',
    role: '',
    responsibilities: [],
    workload: 100,
    avatar: ''
  })
  emitUpdate()
}

function removeMember(index: number) {
  formData.teamMembers.splice(index, 1)
  emitUpdate()
}

// 成功指标管理
function addMetric() {
  formData.successMetrics.push({
    id: `metric_${Date.now()}`,
    name: '',
    description: '',
    type: 'quantitative',
    unit: '',
    baselineValue: null,
    targetValue: null,
    actualValue: null,
    measurementMethod: '',
    frequency: 'milestone',
    dataSource: '',
    responsible: ''
  })
  emitUpdate()
}

function removeMetric(index: number) {
  formData.successMetrics.splice(index, 1)
  emitUpdate()
}

// 发射更新事件
function emitUpdate() {
  const updatedCycle = {
    ...props.cycle,
    ...formData,
    startDate: formData.startDate ? formData.startDate.format('YYYY-MM-DD') : null,
    endDate: formData.endDate ? formData.endDate.format('YYYY-MM-DD') : null
  }
  emit('update:cycle', updatedCycle)
}

// 监听表单数据变化
watch(formData, () => {
  emitUpdate()
}, { deep: true })

// 监听props变化
watch(() => props.cycle, () => {
  initFormData()
}, { immediate: true, deep: true })

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped lang="less">
.cycle-info-form {
  .team-members-section,
  .success-metrics-section {
    .member-item,
    .metric-item {
      margin-bottom: 8px;
      padding: 8px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      background: #fafafa;
    }

    .members-list,
    .metrics-list {
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
