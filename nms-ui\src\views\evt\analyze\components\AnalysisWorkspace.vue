<!--
  分析工作区组件
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <a-card 
    title="分析工作区" 
    class="analysis-workspace"
    :loading="loading"
    :tab-list="workspaceTabs"
    :active-tab-key="activeTab"
    @tab-change="handleTabChange"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleSave" :loading="saving" v-if="!readonly">
          <template #icon><SaveOutlined /></template>
          保存
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitting" v-if="!readonly && canSubmit">
          <template #icon><CheckOutlined /></template>
          提交分析
        </a-button>
      </a-space>
    </template>

    <!-- 参会信息标签页 -->
    <div v-show="activeTab === 'meeting'" class="tab-content">
      <a-form layout="vertical" :model="analysisData.meetingInfo">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="会议时间">
              <a-date-picker
                :value="ensureDayjsObject(analysisData.meetingInfo.meetingTime)"
                @change="(date) => analysisData.meetingInfo.meetingTime = ensureDayjsObject(date)"
                show-time
                style="width: 100%"
                :disabled="readonly"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="会议地点">
              <a-input 
                v-model:value="analysisData.meetingInfo.location"
                placeholder="请输入会议地点"
                :disabled="readonly"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="会议主持人">
          <a-input 
            v-model:value="analysisData.meetingInfo.moderator"
            placeholder="请输入主持人姓名"
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="参会人员">
          <a-textarea 
            v-model:value="analysisData.meetingInfo.participants"
            :rows="3"
            placeholder="请输入参会人员信息"
            :disabled="readonly"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 原因分析标签页 -->
    <div v-show="activeTab === 'causes'" class="tab-content">
      <div class="causes-section">
        <div class="section-header">
          <h4>原因分析</h4>
          <a-button type="dashed" @click="handleAddCause" v-if="!readonly">
            <template #icon><PlusOutlined /></template>
            添加原因
          </a-button>
        </div>
        
        <div v-if="analysisData.causes.length > 0" class="causes-list">
          <div 
            v-for="(cause, index) in analysisData.causes" 
            :key="cause.id || index"
            class="cause-item"
          >
            <a-card size="small">
              <template #title>
                <a-select 
                  v-model:value="cause.type"
                  placeholder="选择原因类型"
                  style="width: 150px"
                  :disabled="readonly"
                >
                  <a-select-option value="direct">直接原因</a-select-option>
                  <a-select-option value="root">根本原因</a-select-option>
                  <a-select-option value="contributing">促成因素</a-select-option>
                </a-select>
              </template>
              <template #extra v-if="!readonly">
                <a-button type="text" danger @click="handleRemoveCause(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
              
              <a-textarea 
                v-model:value="cause.description"
                placeholder="请描述具体原因..."
                :rows="3"
                :disabled="readonly"
              />
            </a-card>
          </div>
        </div>
        
        <a-empty v-else description="暂无原因分析" />
      </div>
    </div>

    <!-- 对策措施标签页 -->
    <div v-show="activeTab === 'measures'" class="tab-content">
      <div class="measures-section">
        <div class="section-header">
          <h4>改进措施</h4>
          <a-button type="dashed" @click="handleAddMeasure" v-if="!readonly">
            <template #icon><PlusOutlined /></template>
            添加措施
          </a-button>
        </div>
        
        <div v-if="analysisData.measures.length > 0" class="measures-list">
          <div 
            v-for="(measure, index) in analysisData.measures" 
            :key="measure.id || index"
            class="measure-item"
          >
            <a-card size="small">
              <template #title>
                措施 {{ index + 1 }}
              </template>
              <template #extra v-if="!readonly">
                <a-button type="text" danger @click="handleRemoveMeasure(index)">
                  <DeleteOutlined />
                </a-button>
              </template>
              
              <a-form layout="vertical">
                <a-form-item label="措施描述">
                  <a-textarea 
                    v-model:value="measure.description"
                    placeholder="请描述具体改进措施..."
                    :rows="2"
                    :disabled="readonly"
                  />
                </a-form-item>
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item label="优先级">
                      <a-select 
                        v-model:value="measure.priority"
                        placeholder="选择优先级"
                        :disabled="readonly"
                      >
                        <a-select-option value="high">高</a-select-option>
                        <a-select-option value="medium">中</a-select-option>
                        <a-select-option value="low">低</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="责任人">
                      <a-input 
                        v-model:value="measure.responsible"
                        placeholder="责任人"
                        :disabled="readonly"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="完成期限">
                      <a-date-picker
                        :value="ensureDayjsObject(measure.deadline)"
                        @change="(date) => measure.deadline = ensureDayjsObject(date)"
                        style="width: 100%"
                        :disabled="readonly"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-card>
          </div>
        </div>
        
        <a-empty v-else description="暂无改进措施" />
      </div>
    </div>

    <!-- 原因对策关联标签页 -->
    <div v-show="activeTab === 'relations'" class="tab-content">
      <CauseMeasureRelation
        v-model:value="analysisData.relations"
        :causes="analysisData.causes"
        :measures="analysisData.measures"
        :readonly="readonly"
        @change="handleRelationsChange"
      />
    </div>

    <!-- 分析工具标签页 -->
    <div v-show="activeTab === 'tools'" class="tab-content">
      <div class="tools-section">
        <h4>分析工具</h4>
        <a-row :gutter="[16, 16]">
          <a-col :span="6" v-for="tool in analysisTools" :key="tool.key">
            <a-card 
              hoverable 
              class="tool-card"
              @click="handleToolSelect(tool)"
              :class="{ 'selected': selectedTool === tool.key }"
            >
              <div class="tool-content">
                <div class="tool-icon">
                  <component :is="tool.icon" />
                </div>
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-desc">{{ tool.description }}</div>
              </div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 工具使用区域 -->
        <div v-if="selectedTool" class="tool-workspace">
          <a-card :title="`${getSelectedToolName()} 分析`">
            <!-- 鱼骨图工具 -->
            <FishboneChart
              v-if="selectedTool === 'fishbone'"
              :data="toolsData.fishbone"
              :readonly="readonly"
              :event-id="eventId"
              :analysis-id="analysisId"
              @data-change="handleFishboneDataChange"
            />

            <!-- 5Why分析工具 -->
            <FiveWhyAnalysis
              v-else-if="selectedTool === '5why'"
              :data="toolsData.fiveWhy"
              :readonly="readonly"
              :event-id="eventId"
              :analysis-id="analysisId"
              @data-change="handleFiveWhyDataChange"
            />

            <!-- PDCA分析工具 -->
            <PDCAAnalysis
              v-else-if="selectedTool === 'pdca'"
              :data="toolsData.pdca"
              :readonly="readonly"
              @data-change="handlePdcaDataChange"
            />

            <!-- FMEA分析工具 -->
            <FMEAAnalysis
              v-else-if="selectedTool === 'fmea'"
              :data="toolsData.fmea"
              :readonly="readonly"
              @data-change="handleFmeaDataChange"
            />

            <!-- 默认占位符 -->
            <div v-else class="tool-placeholder">
              <a-result
                status="info"
                title="分析工具工作区"
                :sub-title="`正在使用 ${getSelectedToolName()} 进行分析`"
              >
                <template #extra>
                  <a-button type="primary" @click="handleStartAnalysis">开始分析</a-button>
                </template>
              </a-result>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 分析结论标签页 -->
    <div v-show="activeTab === 'conclusion'" class="tab-content">
      <a-form layout="vertical" :model="analysisData.conclusion">
        <a-form-item label="分析结论">
          <a-textarea 
            v-model:value="analysisData.conclusion.summary"
            :rows="4"
            placeholder="请输入分析结论..."
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="改进建议">
          <a-textarea 
            v-model:value="analysisData.conclusion.recommendations"
            :rows="4"
            placeholder="请输入改进建议..."
            :disabled="readonly"
          />
        </a-form-item>
        <a-form-item label="预期效果">
          <a-textarea 
            v-model:value="analysisData.conclusion.expectedOutcome"
            :rows="3"
            placeholder="请描述预期改进效果..."
            :disabled="readonly"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  CheckOutlined,
  PlusOutlined,
  DeleteOutlined,
  BarChartOutlined,
  BulbOutlined,
  NodeIndexOutlined,
  BranchesOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

// 导入分析工具组件
import FishboneChart from './FishboneChart.vue'
import FiveWhyAnalysis from './FiveWhyAnalysis.vue'
import PDCAAnalysis from './PDCAAnalysis.vue'
import FMEAAnalysis from './FMEAAnalysis.vue'
import CauseMeasureRelation from './CauseMeasureRelation.vue'

// 定义Props
interface Props {
  loading?: boolean
  readonly?: boolean
  initialData?: any
  eventId?: string
  analysisId?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  readonly: false,
  initialData: () => ({})
})

// 定义Emits
const emit = defineEmits<{
  save: [data: any]
  submit: [data: any]
  dataChange: [data: any]
}>()

// 响应式数据
const activeTab = ref('meeting')
const saving = ref(false)
const submitting = ref(false)
const selectedTool = ref('')

// 工作区标签页配置
const workspaceTabs = [
  { key: 'meeting', tab: '参会信息' },
  { key: 'causes', tab: '原因分析' },
  { key: 'measures', tab: '对策措施' },
  { key: 'relations', tab: '原因对策关联' },
  { key: 'tools', tab: '分析工具' },
  { key: 'conclusion', tab: '分析结论' }
]

// 分析工具配置
const analysisTools = [
  { key: 'pdca', name: 'PDCA分析', description: '计划-执行-检查-行动', icon: BarChartOutlined },
  { key: '5why', name: '5Why分析', description: '五个为什么分析法', icon: BulbOutlined },
  { key: 'fmea', name: 'FMEA分析', description: '失效模式与影响分析', icon: NodeIndexOutlined },
  { key: 'fishbone', name: '鱼骨图分析', description: '因果关系分析', icon: BranchesOutlined }
]



// 分析数据
const analysisData = reactive({
  meetingInfo: {
    meetingTime: null,
    location: '',
    moderator: '',
    participants: ''
  },
  causes: [],
  measures: [],
  relations: [], // 原因对策关联关系
  conclusion: {
    summary: '',
    recommendations: '',
    expectedOutcome: ''
  }
})

// 分析工具数据
const toolsData = reactive({
  fishbone: {
    problem: '',
    mainBones: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  fiveWhy: {
    problem: '',
    whyLevels: [],
    conclusion: '',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  pdca: {
    cycleName: '',
    plan: [],
    do: [],
    check: [],
    act: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  },
  fmea: {
    processName: '',
    riskItems: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
  }
})

// 计算属性
const canSubmit = computed(() => {
  return analysisData.causes.length > 0 && 
         analysisData.measures.length > 0 && 
         analysisData.conclusion.summary
})

const getSelectedToolName = () => {
  const tool = analysisTools.find(t => t.key === selectedTool.value)
  return tool ? tool.name : ''
}

// 事件处理
const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleSave = async () => {
  saving.value = true
  try {
    emit('save', analysisData)
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    emit('submit', analysisData)
    message.success('提交成功')
  } catch (error) {
    message.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const handleAddCause = () => {
  analysisData.causes.push({
    id: `cause_${Date.now()}`,
    type: 'direct',
    description: ''
  })
}

const handleRemoveCause = (index: number) => {
  analysisData.causes.splice(index, 1)
}

const handleAddMeasure = () => {
  analysisData.measures.push({
    id: `measure_${Date.now()}`,
    description: '',
    priority: 'medium',
    responsible: '',
    deadline: null // 保持为null，让用户选择时会自动转换为dayjs对象
  })
}

const handleRemoveMeasure = (index: number) => {
  analysisData.measures.splice(index, 1)
}

// 监听日期字段变化，确保格式正确
watch(() => analysisData.meetingInfo.meetingTime, (newValue) => {
  if (newValue && !dayjs.isDayjs(newValue)) {
    analysisData.meetingInfo.meetingTime = ensureDayjsObject(newValue)
  }
})

// 监听措施的deadline字段变化
watch(() => analysisData.measures, (newMeasures) => {
  newMeasures.forEach((measure, index) => {
    if (measure.deadline && !dayjs.isDayjs(measure.deadline)) {
      measure.deadline = ensureDayjsObject(measure.deadline)
    }
  })
}, { deep: true })

// 处理原因对策关联变化
const handleRelationsChange = (relations: any[]) => {
  analysisData.relations = relations
  emit('dataChange', { analysisData, toolsData })
}

const handleToolSelect = (tool: any) => {
  selectedTool.value = tool.key
  message.info(`已选择 ${tool.name}`)
}

const handleStartAnalysis = () => {
  message.info('开始分析功能开发中...')
}

// 分析工具数据变化处理
const handleFishboneDataChange = (data: any) => {
  Object.assign(toolsData.fishbone, data)

  // 从鱼骨图数据中提取原因
  if (data.mainBones && data.mainBones.length > 0) {
    syncCausesFromFishbone(data.mainBones)
  }

  emit('dataChange', { analysisData, toolsData })
}

const handleFiveWhyDataChange = (data: any) => {
  Object.assign(toolsData.fiveWhy, data)

  // 从5Why分析中提取原因
  if (data.whyLevels && data.whyLevels.length > 0) {
    syncCausesFromFiveWhy(data.whyLevels)
  }

  emit('dataChange', { analysisData, toolsData })
}

const handlePdcaDataChange = (data: any) => {
  Object.assign(toolsData.pdca, data)
  emit('dataChange', { analysisData, toolsData })
}

const handleFmeaDataChange = (data: any) => {
  Object.assign(toolsData.fmea, data)
  emit('dataChange', { analysisData, toolsData })
}

// 数据同步方法
const syncCausesFromFishbone = (mainBones: any[]) => {
  const newCauses: any[] = []

  mainBones.forEach(bone => {
    if (bone.causes && bone.causes.length > 0) {
      bone.causes.forEach((cause: any) => {
        // 检查是否已存在相同的原因
        const existingCause = analysisData.causes.find(c =>
          c.causeDescription === cause.text || c.description === cause.text
        )

        if (!existingCause && cause.text && cause.text.trim()) {
          newCauses.push({
            id: `cause_fishbone_${Date.now()}_${Math.random()}`,
            causeType: mapBoneCategoryToCauseType(bone.category),
            causeDescription: cause.text,
            level: 2, // 鱼骨图的原因默认为直接原因
            probability: 70, // 默认可能性
            impact: 3, // 默认影响程度
            evidence: '来源：鱼骨图分析',
            source: 'fishbone'
          })
        }
      })
    }
  })

  if (newCauses.length > 0) {
    analysisData.causes.push(...newCauses)
    message.success(`从鱼骨图同步了${newCauses.length}个原因`)
  }
}

const syncCausesFromFiveWhy = (whyLevels: any[]) => {
  const newCauses: any[] = []

  whyLevels.forEach((level, index) => {
    if (level.question && level.question.trim()) {
      // 检查是否已存在相同的原因
      const existingCause = analysisData.causes.find(c =>
        c.causeDescription === level.question || c.description === level.question
      )

      if (!existingCause) {
        newCauses.push({
          id: `cause_fivewhy_${Date.now()}_${Math.random()}`,
          causeType: '管理因素', // 5Why分析通常涉及管理因素
          causeDescription: level.question,
          level: Math.min(index + 1, 4), // 根据层级确定原因层级
          probability: Math.max(90 - index * 15, 30), // 越深层的原因可能性越低
          impact: Math.min(index + 2, 5), // 越深层的原因影响越大
          evidence: '来源：5Why分析',
          source: 'fiveWhy'
        })
      }
    }
  })

  if (newCauses.length > 0) {
    analysisData.causes.push(...newCauses)
    message.success(`从5Why分析同步了${newCauses.length}个原因`)
  }
}

// 映射鱼骨图分类到原因类型
const mapBoneCategoryToCauseType = (category: string): string => {
  const mapping: Record<string, string> = {
    'people': '人员因素',
    'machine': '设备因素',
    'material': '物料因素',
    'method': '方法因素',
    'environment': '环境因素',
    'measurement': '测量因素',
    'management': '管理因素'
  }
  return mapping[category] || '其他因素'
}

// 监听分析数据变化
watch(analysisData, () => {
  emit('dataChange', { analysisData, toolsData })
}, { deep: true })

// 初始化数据
if (props.initialData) {
  if (props.initialData.analysisData) {
    Object.assign(analysisData, props.initialData.analysisData)
  }
  if (props.initialData.toolsData) {
    Object.assign(toolsData, props.initialData.toolsData)
  }
}
</script>

<style scoped lang="less">
.analysis-workspace {
  .tab-content {
    padding: 16px 0;
    min-height: 400px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      color: #333;
    }
  }

  .causes-list, .measures-list {
    .cause-item, .measure-item {
      margin-bottom: 12px;
    }
  }

  .tools-section {
    .tool-card {
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }

      &.selected {
        border-color: #1890ff;
        background-color: #f6ffed;
      }

      .tool-content {
        .tool-icon {
          font-size: 24px;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .tool-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .tool-desc {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .tool-workspace {
      margin-top: 24px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .analysis-workspace {
    .tools-section {
      .ant-col {
        span: 12 !important;
      }
    }
  }
}
</style>
