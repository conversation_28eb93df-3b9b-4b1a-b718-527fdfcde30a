<!--
  分析结论表单组件
  
  基于EventReportPage.vue的组件设计模式，提供分析结论的表单录入功能
  
  <AUTHOR>
  @version 1.0.0
  @since 2025-02-14
-->

<template>
  <div class="analysis-conclusion-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      :disabled="readonly"
      @finish="handleSubmit"
      @valuesChange="handleValuesChange"
    >
      <!-- 分析总结 -->
      <div class="form-section">
        <h4 class="section-title">分析总结</h4>
        
        <a-form-item
          label="事件概述"
          name="eventSummary"
          :rules="[{ required: true, message: '请输入事件概述' }]"
        >
          <a-textarea
            v-model:value="formData.eventSummary"
            placeholder="请简要概述事件的基本情况"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item
          label="主要原因"
          name="mainCauses"
          :rules="[{ required: true, message: '请输入主要原因' }]"
        >
          <a-textarea
            v-model:value="formData.mainCauses"
            placeholder="请总结分析得出的主要原因"
            :rows="4"
            :maxlength="800"
            show-count
          />
        </a-form-item>
        
        <a-form-item
          label="关键措施"
          name="keyMeasures"
          :rules="[{ required: true, message: '请输入关键措施' }]"
        >
          <a-textarea
            v-model:value="formData.keyMeasures"
            placeholder="请总结制定的关键对策措施"
            :rows="4"
            :maxlength="800"
            show-count
          />
        </a-form-item>
      </div>

      <!-- 分析结论 -->
      <div class="form-section">
        <h4 class="section-title">分析结论</h4>
        
        <a-form-item
          label="结论描述"
          name="conclusion"
          :rules="[{ required: true, message: '请输入分析结论' }]"
        >
          <a-textarea
            v-model:value="formData.conclusion"
            placeholder="请详细描述分析结论"
            :rows="5"
            :maxlength="1000"
            show-count
          />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="风险等级评估"
              name="riskLevel"
              :rules="[{ required: true, message: '请选择风险等级' }]"
            >
              <a-select
                v-model:value="formData.riskLevel"
                placeholder="请选择风险等级"
              >
                <a-select-option value="low">
                  <a-tag color="green">低风险</a-tag>
                </a-select-option>
                <a-select-option value="medium">
                  <a-tag color="orange">中风险</a-tag>
                </a-select-option>
                <a-select-option value="high">
                  <a-tag color="red">高风险</a-tag>
                </a-select-option>
                <a-select-option value="critical">
                  <a-tag color="red">极高风险</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              label="复发可能性"
              name="recurrenceProbability"
            >
              <a-input-number
                v-model:value="formData.recurrenceProbability"
                :min="0"
                :max="100"
                placeholder="0-100"
                style="width: 100%"
                :formatter="value => `${value}%`"
                :parser="value => value.replace('%', '')"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 改进建议 -->
      <div class="form-section">
        <h4 class="section-title">改进建议</h4>
        
        <a-form-item
          label="短期建议"
          name="shortTermRecommendations"
        >
          <a-textarea
            v-model:value="formData.shortTermRecommendations"
            placeholder="请提出短期内需要实施的改进建议"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item
          label="长期建议"
          name="longTermRecommendations"
        >
          <a-textarea
            v-model:value="formData.longTermRecommendations"
            placeholder="请提出长期的系统性改进建议"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-form-item
          label="监控要点"
          name="monitoringPoints"
        >
          <a-textarea
            v-model:value="formData.monitoringPoints"
            placeholder="请提出需要持续监控的要点"
            :rows="2"
            :maxlength="300"
            show-count
          />
        </a-form-item>
      </div>

      <!-- 分析质量评估 -->
      <div class="form-section">
        <h4 class="section-title">分析质量评估</h4>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="分析深度">
              <a-rate 
                v-model:value="formData.analysisDepth" 
                :count="5"
                allow-half
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="证据充分性">
              <a-rate 
                v-model:value="formData.evidenceAdequacy" 
                :count="5"
                allow-half
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="措施可行性">
              <a-rate 
                v-model:value="formData.measureFeasibility" 
                :count="5"
                allow-half
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="分析局限性">
          <a-textarea
            v-model:value="formData.limitations"
            placeholder="请说明本次分析的局限性和不足"
            :rows="2"
            :maxlength="300"
            show-count
          />
        </a-form-item>
      </div>

      <!-- 后续跟进 -->
      <div class="form-section">
        <h4 class="section-title">后续跟进</h4>
        
        <a-form-item
          label="跟进计划"
          name="followUpPlan"
        >
          <a-textarea
            v-model:value="formData.followUpPlan"
            placeholder="请制定后续跟进计划"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="下次评估时间">
              <a-date-picker
                v-model:value="formData.nextReviewDate"
                placeholder="选择下次评估时间"
                style="width: 100%"
                :value="ensureDayjsObject(formData.nextReviewDate)"
                @change="(date) => formData.nextReviewDate = ensureDayjsObject(date)"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="跟进负责人">
              <a-select
                v-model:value="formData.followUpResponsible"
                placeholder="选择跟进负责人"
                show-search
                :filter-option="filterOption"
                :loading="loadingUsers"
                @dropdown-visible-change="handleUserDropdown"
              >
                <a-select-option
                  v-for="user in userList"
                  :key="user.id"
                  :value="user.id"
                >
                  {{ user.name }} ({{ user.department }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 表单操作 -->
      <div v-if="!readonly" class="form-actions">
        <a-space>
          <a-button @click="handleReset">重置</a-button>
          <a-button @click="handleSaveDraft" :loading="saving">
            保存草稿
          </a-button>
          <a-button type="primary" html-type="submit" :loading="saving">
            完成分析
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

// ============================ Props定义 ============================

export interface AnalysisConclusion {
  eventSummary?: string
  mainCauses?: string
  keyMeasures?: string
  conclusion?: string
  riskLevel?: string
  recurrenceProbability?: number
  shortTermRecommendations?: string
  longTermRecommendations?: string
  monitoringPoints?: string
  analysisDepth?: number
  evidenceAdequacy?: number
  measureFeasibility?: number
  limitations?: string
  followUpPlan?: string
  nextReviewDate?: Dayjs | null
  followUpResponsible?: string
}

export interface Props {
  value?: AnalysisConclusion
  analysisData?: any
  readonly?: boolean
  saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: () => ({}),
  readonly: false,
  saving: false
})

// ============================ Emits定义 ============================

const emit = defineEmits<{
  'update:value': [value: AnalysisConclusion]
  change: [value: AnalysisConclusion]
  submit: [value: AnalysisConclusion]
  saveDraft: [value: AnalysisConclusion]
}>()

// ============================ 响应式数据 ============================

const formRef = ref<FormInstance>()
const loadingUsers = ref(false)
const userList = ref<Array<{ id: string; name: string; department: string }>>([])

// 表单数据
const formData = reactive<AnalysisConclusion>({
  eventSummary: '',
  mainCauses: '',
  keyMeasures: '',
  conclusion: '',
  riskLevel: '',
  recurrenceProbability: 0,
  shortTermRecommendations: '',
  longTermRecommendations: '',
  monitoringPoints: '',
  analysisDepth: 0,
  evidenceAdequacy: 0,
  measureFeasibility: 0,
  limitations: '',
  followUpPlan: '',
  nextReviewDate: null,
  followUpResponsible: ''
})

// 表单验证规则
const formRules = {
  eventSummary: [{ required: true, message: '请输入事件概述' }],
  mainCauses: [{ required: true, message: '请输入主要原因' }],
  keyMeasures: [{ required: true, message: '请输入关键措施' }],
  conclusion: [{ required: true, message: '请输入分析结论' }],
  riskLevel: [{ required: true, message: '请选择风险等级' }]
}

// ============================ 计算属性和方法 ============================

// 过滤选项
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 监听props变化
watch(() => props.value, (newValue) => {
  if (newValue) {
    Object.assign(formData, newValue)
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:value', newValue)
  emit('change', newValue)
}, { deep: true })

// ============================ 事件处理 ============================

function handleValuesChange() {
  // 表单值变化时的处理
}

function handleSubmit() {
  emit('submit', formData)
}

function handleReset() {
  formRef.value?.resetFields()
}

function handleSaveDraft() {
  emit('saveDraft', formData)
}

function handleUserDropdown(open: boolean) {
  if (open && !userList.value.length) {
    loadUserList()
  }
}

async function loadUserList() {
  loadingUsers.value = true
  try {
    // 模拟加载用户列表
    await new Promise(resolve => setTimeout(resolve, 500))
    
    userList.value = [
      { id: 'user1', name: '张主任', department: '质控科' },
      { id: 'user2', name: '李护士长', department: '护理部' },
      { id: 'user3', name: '王医生', department: '内科' },
      { id: 'user4', name: '赵药师', department: '药剂科' }
    ]
  } catch (error) {
    message.error('加载用户列表失败')
  } finally {
    loadingUsers.value = false
  }
}

// ============================ 生命周期 ============================

onMounted(() => {
  // 初始化时加载用户列表
  loadUserList()
  
  // 如果有分析数据，自动填充一些信息
  if (props.analysisData) {
    if (props.analysisData.causes?.length) {
      const causesText = props.analysisData.causes
        .map((cause: any) => cause.causeDescription)
        .join('；')
      formData.mainCauses = causesText
    }
    
    if (props.analysisData.measures?.length) {
      const measuresText = props.analysisData.measures
        .map((measure: any) => measure.measureDescription)
        .join('；')
      formData.keyMeasures = measuresText
    }
  }
})
</script>

<style scoped lang="less">
.analysis-conclusion-form {
  .form-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .analysis-conclusion-form {
    .form-section {
      :deep(.ant-row) {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }
  }
}
</style>
