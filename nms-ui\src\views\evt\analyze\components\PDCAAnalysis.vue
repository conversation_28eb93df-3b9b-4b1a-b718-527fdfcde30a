<!--
  PDCA循环分析工具组件

  计划-执行-检查-行动的持续改进循环管理工具
  支持多轮循环、任务管理、进度跟踪、效果评估

  <AUTHOR>
  @version 2.0.0
  @since 2025-07-26
-->

<template>
  <div class="pdca-analysis">
    <!-- 工具栏 -->
    <div class="analysis-toolbar">
      <div class="toolbar-left">
        <a-input
          v-model:value="localData.title"
          placeholder="请输入PDCA循环主题"
          style="width: 400px"
          @change="handleDataChange"
          :disabled="readonly"
        >
          <template #prefix>
            <FileTextOutlined />
          </template>
        </a-input>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <a-progress
            :percent="completionPercentage"
            size="small"
            :show-info="false"
            :stroke-color="getProgressColor()"
          />
          <span class="progress-text">{{ completionPercentage }}% 完成</span>
        </div>
      </div>

      <div class="toolbar-right">
        <a-space>
          <!-- 自动保存状态 -->
          <div class="auto-save-status" v-if="!readonly">
            <a-spin v-if="isSaving" size="small" />
            <CheckCircleOutlined v-else-if="lastSaved" style="color: #52c41a" />
            <span class="save-text">
              {{ isSaving ? '保存中...' : lastSaved ? `已保存 ${formatTime(lastSaved)}` : '未保存' }}
            </span>
          </div>

          <a-button size="small" @click="handleReset" :disabled="readonly" :loading="isResetting">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
          <a-button size="small" @click="handleExport" :loading="isExporting">
            <template #icon><ExportOutlined /></template>
            导出
          </a-button>

          <!-- 帮助按钮 -->
          <a-button size="small" @click="showHelp" type="text">
            <template #icon><QuestionCircleOutlined /></template>
            帮助
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- PDCA循环图 -->
    <div class="pdca-cycle">
      <div class="cycle-container">
        <!-- 中心标题 -->
        <div class="cycle-center">
          <div class="center-title">PDCA</div>
          <div class="center-subtitle">持续改进</div>
        </div>

        <!-- Plan阶段 -->
        <div class="cycle-section plan-section" :class="{ active: activePhase === 'plan' }">
          <div class="section-header" @click="setActivePhase('plan')">
            <div class="section-icon">📋</div>
            <div class="section-title">Plan<br>计划</div>
          </div>
        </div>

        <!-- Do阶段 -->
        <div class="cycle-section do-section" :class="{ active: activePhase === 'do' }">
          <div class="section-header" @click="setActivePhase('do')">
            <div class="section-icon">🚀</div>
            <div class="section-title">Do<br>执行</div>
          </div>
        </div>

        <!-- Check阶段 -->
        <div class="cycle-section check-section" :class="{ active: activePhase === 'check' }">
          <div class="section-header" @click="setActivePhase('check')">
            <div class="section-icon">🔍</div>
            <div class="section-title">Check<br>检查</div>
          </div>
        </div>

        <!-- Act阶段 -->
        <div class="cycle-section act-section" :class="{ active: activePhase === 'act' }">
          <div class="section-header" @click="setActivePhase('act')">
            <div class="section-icon">⚡</div>
            <div class="section-title">Act<br>行动</div>
          </div>
        </div>

        <!-- 循环箭头 -->
        <div class="cycle-arrows">
          <div class="arrow arrow-1">→</div>
          <div class="arrow arrow-2">↓</div>
          <div class="arrow arrow-3">←</div>
          <div class="arrow arrow-4">↑</div>
        </div>
      </div>
    </div>

    <!-- 阶段详情 -->
    <div class="phase-details">
      <a-card :title="currentPhaseInfo.title" class="details-card">
        <div class="phase-content">
          <div class="phase-description">
            <p>{{ currentPhaseInfo.description }}</p>
          </div>

          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item :label="currentPhaseInfo.fields.field1.label">
                  <a-textarea
                    v-model:value="localData[activePhase][currentPhaseInfo.fields.field1.key]"
                    :placeholder="currentPhaseInfo.fields.field1.placeholder"
                    :rows="4"
                    :readonly="readonly"
                    @change="handleDataChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :label="currentPhaseInfo.fields.field2.label">
                  <a-textarea
                    v-model:value="localData[activePhase][currentPhaseInfo.fields.field2.key]"
                    :placeholder="currentPhaseInfo.fields.field2.placeholder"
                    :rows="4"
                    :readonly="readonly"
                    @change="handleDataChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="负责人">
                  <a-input
                    v-model:value="localData[activePhase].responsible"
                    placeholder="请输入负责人"
                    :readonly="readonly"
                    @change="handleDataChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="完成时间">
                  <a-date-picker
                    v-model:value="localData[activePhase].deadline"
                    placeholder="请选择完成时间"
                    style="width: 100%"
                    :disabled="readonly"
                    @change="handleDataChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="状态">
              <a-radio-group 
                v-model:value="localData[activePhase].status"
                :disabled="readonly"
                @change="handleDataChange"
              >
                <a-radio value="pending">待开始</a-radio>
                <a-radio value="in_progress">进行中</a-radio>
                <a-radio value="completed">已完成</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
      </a-card>
    </div>

    <!-- 进度总览 -->
    <div class="progress-overview">
      <a-card title="PDCA循环进度" class="progress-card">
        <div class="progress-items">
          <div 
            v-for="phase in phases" 
            :key="phase.key"
            class="progress-item"
            :class="{ completed: localData[phase.key].status === 'completed' }"
          >
            <div class="progress-icon">
              {{ phase.icon }}
            </div>
            <div class="progress-info">
              <div class="progress-title">{{ phase.title }}</div>
              <div class="progress-status">
                <a-tag :color="getStatusColor(localData[phase.key].status)">
                  {{ getStatusText(localData[phase.key].status) }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { ensureDayjsObject, createDatePickerProps } from '@/utils/dateUtils'

interface PhaseData {
  status: 'pending' | 'in_progress' | 'completed'
  responsible: string
  deadline: any
  [key: string]: any
}

interface PDCAData {
  title: string
  plan: PhaseData & {
    objectives: string
    methods: string
  }
  do: PhaseData & {
    actions: string
    resources: string
  }
  check: PhaseData & {
    metrics: string
    results: string
  }
  act: PhaseData & {
    improvements: string
    standardization: string
  }
}

interface Props {
  data?: PDCAData
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  data: () => ({
    title: '',
    plan: { status: 'pending', responsible: '', deadline: null, objectives: '', methods: '' },
    do: { status: 'pending', responsible: '', deadline: null, actions: '', resources: '' },
    check: { status: 'pending', responsible: '', deadline: null, metrics: '', results: '' },
    act: { status: 'pending', responsible: '', deadline: null, improvements: '', standardization: '' }
  })
})

const emit = defineEmits<{
  'data-change': [data: PDCAData]
  save: [data: PDCAData]
  export: [data: any]
}>()

// 本地数据
const localData = reactive<PDCAData>({
  title: props.data?.title || '',
  plan: props.data?.plan || { status: 'pending', responsible: '', deadline: null, objectives: '', methods: '' },
  do: props.data?.do || { status: 'pending', responsible: '', deadline: null, actions: '', resources: '' },
  check: props.data?.check || { status: 'pending', responsible: '', deadline: null, metrics: '', results: '' },
  act: props.data?.act || { status: 'pending', responsible: '', deadline: null, improvements: '', standardization: '' }
})

// 当前活动阶段
const activePhase = ref<'plan' | 'do' | 'check' | 'act'>('plan')

// 状态管理
const isSaving = ref(false)
const isResetting = ref(false)
const isExporting = ref(false)
const lastSaved = ref<Date | null>(null)

// 阶段定义
const phases = [
  { key: 'plan', title: 'Plan 计划', icon: '📋' },
  { key: 'do', title: 'Do 执行', icon: '🚀' },
  { key: 'check', title: 'Check 检查', icon: '🔍' },
  { key: 'act', title: 'Act 行动', icon: '⚡' }
]

// 阶段信息
const phaseInfoMap = {
  plan: {
    title: 'Plan - 计划阶段',
    description: '制定目标和计划，确定要解决的问题和改进方法',
    fields: {
      field1: { key: 'objectives', label: '目标设定', placeholder: '请描述要达成的目标' },
      field2: { key: 'methods', label: '方法计划', placeholder: '请描述实现目标的方法和步骤' }
    }
  },
  do: {
    title: 'Do - 执行阶段',
    description: '按照计划执行，实施改进措施',
    fields: {
      field1: { key: 'actions', label: '执行行动', placeholder: '请描述具体的执行行动' },
      field2: { key: 'resources', label: '资源配置', placeholder: '请描述所需的资源和支持' }
    }
  },
  check: {
    title: 'Check - 检查阶段',
    description: '检查执行结果，评估改进效果',
    fields: {
      field1: { key: 'metrics', label: '检查指标', placeholder: '请描述检查的指标和标准' },
      field2: { key: 'results', label: '检查结果', placeholder: '请描述检查的结果和发现' }
    }
  },
  act: {
    title: 'Act - 行动阶段',
    description: '根据检查结果采取行动，标准化成功做法',
    fields: {
      field1: { key: 'improvements', label: '改进措施', placeholder: '请描述进一步的改进措施' },
      field2: { key: 'standardization', label: '标准化', placeholder: '请描述如何标准化成功做法' }
    }
  }
}

// 当前阶段信息
const currentPhaseInfo = computed(() => phaseInfoMap[activePhase.value])

// 完成度计算
const completionPercentage = computed(() => {
  const phases = ['plan', 'do', 'check', 'act']
  let completedCount = 0

  phases.forEach(phase => {
    const phaseData = localData[phase]
    if (phaseData.status === 'completed') {
      completedCount++
    } else if (phaseData.status === 'in_progress') {
      completedCount += 0.5
    }
  })

  return Math.round((completedCount / phases.length) * 100)
})

// 进度颜色
const getProgressColor = () => {
  const percentage = completionPercentage.value
  if (percentage >= 80) return '#52c41a'
  if (percentage >= 50) return '#faad14'
  return '#ff4d4f'
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`

  const days = Math.floor(hours / 24)
  return `${days}天前`
}

// 设置活动阶段
function setActivePhase(phase: 'plan' | 'do' | 'check' | 'act') {
  activePhase.value = phase
}

// 获取状态颜色
function getStatusColor(status: string) {
  const colorMap = {
    pending: 'default',
    in_progress: 'processing',
    completed: 'success'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
function getStatusText(status: string) {
  const textMap = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成'
  }
  return textMap[status] || '未知'
}

// 重置分析
async function handleReset() {
  try {
    isResetting.value = true

    // 确认重置
    await new Promise((resolve, reject) => {
      Modal.confirm({
        title: '确认重置',
        content: '重置将清空所有PDCA分析数据，此操作不可恢复。确定要继续吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: resolve,
        onCancel: reject
      })
    })

    Object.assign(localData, {
      title: '',
      plan: { status: 'pending', responsible: '', deadline: null, objectives: '', methods: '' },
      do: { status: 'pending', responsible: '', deadline: null, actions: '', resources: '' },
      check: { status: 'pending', responsible: '', deadline: null, metrics: '', results: '' },
      act: { status: 'pending', responsible: '', deadline: null, improvements: '', standardization: '' }
    })

    activePhase.value = 'plan'
    lastSaved.value = null

    handleDataChange()
    message.success('已重置PDCA分析')

  } catch (error) {
    // 用户取消重置
  } finally {
    isResetting.value = false
  }
}

// 显示帮助
function showHelp() {
  Modal.info({
    title: 'PDCA分析工具使用帮助',
    width: 600,
    content: `
      <div style="line-height: 1.6;">
        <h4>PDCA循环分析法</h4>
        <p>PDCA是一个持续改进的循环过程，包含四个阶段：</p>

        <h5>📋 Plan（计划）</h5>
        <p>• 设定改进目标和成功标准</p>
        <p>• 制定具体的实施方法和步骤</p>
        <p>• 分配责任人和完成时间</p>

        <h5>🚀 Do（执行）</h5>
        <p>• 按照计划执行改进行动</p>
        <p>• 配置必要的资源和支持</p>
        <p>• 记录执行过程和遇到的问题</p>

        <h5>🔍 Check（检查）</h5>
        <p>• 设定检查指标和评估标准</p>
        <p>• 收集和分析执行结果数据</p>
        <p>• 评估是否达到预期目标</p>

        <h5>⚡ Act（行动）</h5>
        <p>• 根据检查结果制定进一步改进措施</p>
        <p>• 将成功的做法标准化和推广</p>
        <p>• 为下一个PDCA循环做准备</p>

        <h4>使用技巧</h4>
        <p>• 点击循环图中的阶段可以切换编辑内容</p>
        <p>• 系统会自动保存您的输入内容</p>
        <p>• 完成每个阶段后记得更新状态</p>
        <p>• 可以随时导出分析结果</p>
      </div>
    `
  })
}

// 数据变更处理
function handleDataChange() {
  emit('data-change', { ...localData })

  // 触发自动保存
  if (!props.readonly) {
    autoSave()
  }
}

// 自动保存
let autoSaveTimer: NodeJS.Timeout | null = null
function autoSave() {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }

  autoSaveTimer = setTimeout(async () => {
    try {
      isSaving.value = true

      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      lastSaved.value = new Date()
      emit('save', { ...localData })

    } catch (error) {
      console.error('自动保存失败:', error)
    } finally {
      isSaving.value = false
    }
  }, 2000) // 2秒后自动保存
}

// 导出分析结果
async function handleExport() {
  try {
    isExporting.value = true

    // 生成导出数据
    const exportData = {
      title: localData.title || 'PDCA分析报告',
      exportTime: new Date().toISOString(),
      completionPercentage: completionPercentage.value,
      phases: {
        plan: {
          ...localData.plan,
          phaseTitle: 'Plan - 计划阶段',
          description: '制定目标和计划，确定要解决的问题和改进方法'
        },
        do: {
          ...localData.do,
          phaseTitle: 'Do - 执行阶段',
          description: '按照计划执行，实施改进措施'
        },
        check: {
          ...localData.check,
          phaseTitle: 'Check - 检查阶段',
          description: '检查执行结果，评估改进效果'
        },
        act: {
          ...localData.act,
          phaseTitle: 'Act - 行动阶段',
          description: '根据检查结果采取行动，标准化成功做法'
        }
      }
    }

    // 模拟导出处理
    await new Promise(resolve => setTimeout(resolve, 1000))

    emit('export', exportData)
    message.success('PDCA分析报告导出成功')

  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  } finally {
    isExporting.value = false
  }
}



// 监听props变化
watch(() => props.data, (newData) => {
  if (newData) {
    // 处理可能包含日期的数据
    const processedData = { ...newData }

    // 处理actions数组中的日期字段
    if (processedData.actions) {
      processedData.actions = processedData.actions.map((action: any) => ({
        ...action,
        deadline: ensureDayjsObject(action.deadline)
      }))
    }

    Object.assign(localData, processedData)
  }
}, { deep: true })
</script>

<style scoped lang="less">
.pdca-analysis {
  .analysis-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 24px;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;

      .progress-indicator {
        display: flex;
        align-items: center;
        gap: 8px;

        .progress-text {
          font-size: 12px;
          color: #666;
          white-space: nowrap;
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .auto-save-status {
        display: flex;
        align-items: center;
        gap: 4px;

        .save-text {
          font-size: 12px;
          color: #666;
          white-space: nowrap;
        }
      }
    }
  }

  .pdca-cycle {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;

    .cycle-container {
      position: relative;
      width: 300px;
      height: 300px;

      .cycle-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;

        .center-title {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
        }

        .center-subtitle {
          font-size: 12px;
          color: #666;
        }
      }

      .cycle-section {
        position: absolute;
        width: 120px;
        height: 120px;
        border: 2px solid #d9d9d9;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          transform: scale(1.05);
        }

        &.active {
          border-color: #1890ff;
          background: #e6f7ff;
          transform: scale(1.1);
        }

        .section-header {
          text-align: center;

          .section-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          .section-title {
            font-size: 12px;
            font-weight: 500;
            line-height: 1.2;
          }
        }

        &.plan-section {
          top: 0;
          left: 50%;
          transform: translateX(-50%);
        }

        &.do-section {
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }

        &.check-section {
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }

        &.act-section {
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }
      }

      .cycle-arrows {
        .arrow {
          position: absolute;
          font-size: 20px;
          color: #1890ff;
          font-weight: bold;

          &.arrow-1 {
            top: 25%;
            right: 15%;
          }

          &.arrow-2 {
            right: 25%;
            bottom: 15%;
          }

          &.arrow-3 {
            bottom: 25%;
            left: 15%;
          }

          &.arrow-4 {
            left: 25%;
            top: 15%;
          }
        }
      }
    }
  }

  .phase-details {
    margin-bottom: 24px;

    .details-card {
      .phase-description {
        margin-bottom: 16px;
        padding: 12px;
        background: #f6ffed;
        border-radius: 6px;

        p {
          margin: 0;
          color: #52c41a;
        }
      }
    }
  }

  .progress-overview {
    .progress-items {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .progress-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        transition: all 0.3s ease;

        &.completed {
          background: #f6ffed;
          border-color: #b7eb8f;
        }

        .progress-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .progress-info {
          flex: 1;

          .progress-title {
            font-weight: 500;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

// 响应式设计优化
@media (max-width: 1200px) {
  .pdca-analysis {
    .analysis-toolbar {
      .toolbar-left {
        gap: 12px;

        .progress-indicator {
          .progress-text {
            display: none; // 隐藏进度文字
          }
        }
      }
    }

    .pdca-cycle {
      .cycle-container {
        width: 250px;
        height: 250px;

        .cycle-section {
          width: 100px;
          height: 100px;

          .section-header {
            .section-icon {
              font-size: 20px;
            }

            .section-title {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .pdca-analysis {
    .analysis-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }

      .toolbar-left {
        flex-direction: column;
        gap: 8px;

        .progress-indicator {
          justify-content: center;

          .progress-text {
            display: inline; // 重新显示进度文字
          }
        }
      }

      .toolbar-right {
        .auto-save-status {
          .save-text {
            display: none; // 隐藏保存状态文字
          }
        }
      }
    }

    .pdca-cycle {
      .cycle-container {
        width: 200px;
        height: 200px;

        .cycle-section {
          width: 80px;
          height: 80px;

          .section-header {
            .section-icon {
              font-size: 16px;
              margin-bottom: 4px;
            }

            .section-title {
              font-size: 10px;
              line-height: 1.1;
            }
          }
        }
      }
    }

    .phase-details {
      .details-card {
        :deep(.ant-card-head) {
          padding: 12px 16px;

          .ant-card-head-title {
            font-size: 14px;
          }
        }

        :deep(.ant-card-body) {
          padding: 12px;
        }

        .phase-content {
          .phase-description {
            padding: 8px;
            margin-bottom: 12px;
          }

          :deep(.ant-form-item) {
            margin-bottom: 12px;
          }
        }
      }
    }

    .progress-overview {
      .progress-items {
        grid-template-columns: 1fr;
        gap: 8px;

        .progress-item {
          padding: 8px;

          .progress-icon {
            font-size: 20px;
            margin-right: 8px;
          }

          .progress-info {
            .progress-title {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .pdca-analysis {
    .analysis-toolbar {
      padding: 12px;

      .toolbar-left {
        :deep(.ant-input) {
          font-size: 14px;
        }
      }

      .toolbar-right {
        :deep(.ant-btn) {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;

          .anticon {
            font-size: 12px;
          }
        }
      }
    }

    .pdca-cycle {
      margin-bottom: 16px;

      .cycle-container {
        width: 180px;
        height: 180px;

        .cycle-center {
          .center-title {
            font-size: 18px;
          }

          .center-subtitle {
            font-size: 10px;
          }
        }

        .cycle-section {
          width: 70px;
          height: 70px;

          .section-header {
            .section-icon {
              font-size: 14px;
              margin-bottom: 2px;
            }

            .section-title {
              font-size: 9px;
              line-height: 1;
            }
          }
        }

        .cycle-arrows {
          .arrow {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 打印样式
@media print {
  .pdca-analysis {
    .analysis-toolbar {
      display: none;
    }

    .pdca-cycle {
      break-inside: avoid;
    }

    .phase-details {
      break-inside: avoid;
      page-break-before: auto;
    }

    .progress-overview {
      break-inside: avoid;
    }
  }
}
</style>
